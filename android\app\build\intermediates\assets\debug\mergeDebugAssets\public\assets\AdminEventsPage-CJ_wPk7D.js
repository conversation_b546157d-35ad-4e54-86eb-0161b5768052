import{k as F,r as l,j as e,g as C,C as g,J as r}from"./index-H1RsCKxW.js";import{M as B}from"./Modal-CNJOtZ7X.js";import{P as A}from"./PageTitle-DQX_PLyu.js";import{a as S}from"./formatters-LGS2Cxr7.js";import{h as f}from"./errorHandler-aLQZqrtb.js";import{P as M}from"./plus-Ci2WE_St.js";import{M as P}from"./message-square-v5Cg0lGj.js";import{S as T,T as q}from"./trash-CavORa9o.js";const R=()=>{const{events:c,loading:j,addEvent:v,updateEvent:N,deleteEvent:y}=F(),[x,i]=l.useState(null),[D,u]=l.useState(!1),[w,h]=l.useState(!1),[n,d]=l.useState(!1),[t,s]=l.useState({title:"",description:"",date:"",time:"",location:""}),p=()=>{s({title:"",description:"",date:"",time:"",location:""})},b=a=>{a?(i(a),s({title:a.title,description:a.description||"",date:a.date,time:a.time||"",location:a.location}),d(!0)):(i(null),p(),d(!1)),u(!0)},o=()=>{u(!1),i(null),p(),d(!1)},k=async a=>{if(a.preventDefault(),!t.title||!t.date||!t.time||!t.location){r.error("Semua field harus diisi");return}try{n&&x?(await N(x.id,{...t,status:"upcoming",createdBy:"admin"}),r.success("Acara berhasil diperbarui di database")):(await v({...t,status:"upcoming",createdBy:"admin"}),r.success("Acara berhasil ditambahkan ke database")),o()}catch(m){f(m,"AdminEventsPage"),r.error("Gagal menyimpan acara ke database")}},E=async a=>{if(window.confirm("Apakah Anda yakin ingin menghapus acara ini?"))try{h(!0),await y(a),r.success("Acara berhasil dihapus dari database")}catch(m){f(m,"AdminEventsPage"),r.error("Gagal menghapus acara dari database")}finally{h(!1)}};return j?e.jsx("div",{className:"flex flex-col items-center justify-center min-h-[50vh]",children:e.jsx(C,{size:"large",variant:"default",text:"Memuat Data Acara dari Database..."})}):e.jsx("div",{className:"min-h-screen bg-[#F9F9F9] text-[#5D534B] p-4 sm:p-8",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8",children:[e.jsx(A,{title:"Kelola Agenda & Kegiatan",borderColor:"border-[#FCE09B]"}),e.jsxs("button",{type:"button",onClick:()=>b(),className:"flex items-center px-3 py-2 sm:px-4 sm:py-2 rounded-lg bg-[#FCE09B] hover:bg-[#f9d572] text-[#5D534B] transition-colors mt-3 sm:mt-0 text-sm sm:text-base",children:[e.jsx(M,{className:"w-4 h-4 mr-2"}),"Tambah Agenda"]})]}),c.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 rounded-lg border border-[#9DE0D2] bg-white",children:[e.jsx(g,{size:64,className:"text-[#9DE0D2] mb-4"}),e.jsx("p",{className:"text-xl font-medium mb-2 text-center",children:"Belum ada agenda"}),e.jsx("p",{className:"text-sm opacity-70 text-center",children:"Tambahkan agenda atau kegiatan baru"})]}):e.jsx("div",{className:"grid gap-4 sm:gap-6",children:c.map(a=>e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg border border-[#9DE0D2] shadow-sm",children:e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start gap-4",children:[e.jsxs("div",{className:"flex-grow min-w-0",children:[e.jsx("h3",{className:"text-lg sm:text-xl font-semibold mb-1 sm:mb-2 break-words",children:a.title}),e.jsx("p",{className:"text-sm opacity-70 mb-3 sm:mb-4 max-h-24 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300",children:a.description||"Tidak ada deskripsi"}),e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(g,{className:"w-4 h-4 mr-2 text-[#9DE0D2] flex-shrink-0"}),S(a.date)," ",a.time]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(P,{className:"w-4 h-4 mr-2 text-[#FCE09B] flex-shrink-0"}),a.location]})]})]}),e.jsxs("div",{className:"flex gap-2 flex-shrink-0 self-end sm:self-start",children:[e.jsx("button",{type:"button",onClick:()=>b(a),className:"p-1.5 sm:p-2 rounded-md sm:rounded-lg hover:bg-gray-100 transition-colors",title:"Edit",children:e.jsx(T,{className:"w-4 h-4 sm:w-5 sm:h-5 text-[#9DE0D2]"})}),e.jsx("button",{type:"button",onClick:()=>E(a.id),className:"p-1.5 sm:p-2 rounded-md sm:rounded-lg hover:bg-gray-100 transition-colors",title:"Hapus",disabled:w,children:e.jsx(q,{className:"w-4 h-4 sm:w-5 sm:h-5 text-[#FF9898]"})})]})]})},a.id))}),e.jsx(B,{isOpen:D,onClose:o,title:n?"Edit Agenda/Kegiatan":"Tambah Agenda/Kegiatan Baru",children:e.jsxs("form",{onSubmit:k,className:"space-y-4 text-[#5D534B]",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"event-title",className:"block text-sm font-medium mb-1",children:"Judul"}),e.jsx("input",{id:"event-title",type:"text",className:"w-full p-2 border border-[#9DE0D2] rounded-lg",placeholder:"Judul agenda/kegiatan",value:t.title,onChange:a=>s({...t,title:a.target.value}),required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"event-description",className:"block text-sm font-medium mb-1",children:"Deskripsi"}),e.jsx("textarea",{id:"event-description",className:"w-full p-2 border border-[#FF9898] rounded-lg",rows:3,placeholder:"Deskripsi agenda/kegiatan",value:t.description,onChange:a=>s({...t,description:a.target.value})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"event-date",className:"block text-sm font-medium mb-1",children:"Tanggal"}),e.jsx("input",{id:"event-date",type:"date",className:"w-full p-2 border border-[#9DE0D2] rounded-lg",value:t.date,onChange:a=>s({...t,date:a.target.value}),required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"event-time",className:"block text-sm font-medium mb-1",children:"Waktu"}),e.jsx("input",{id:"event-time",type:"time",className:"w-full p-2 border border-[#FCE09B] rounded-lg",value:t.time,onChange:a=>s({...t,time:a.target.value}),required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"event-location",className:"block text-sm font-medium mb-1",children:"Lokasi"}),e.jsx("input",{id:"event-location",type:"text",className:"w-full p-2 border border-[#9DE0D2] rounded-lg",placeholder:"Lokasi acara",value:t.location,onChange:a=>s({...t,location:a.target.value}),required:!0})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-end gap-2 sm:gap-4 pt-4",children:[e.jsx("button",{type:"button",onClick:o,className:"px-4 py-2 rounded-lg border border-[#FF9898] text-[#FF9898] hover:bg-[#FF9898] hover:text-white transition-colors order-last sm:order-first",children:"Batal"}),e.jsx("button",{type:"submit",className:"px-4 py-2 rounded-lg bg-[#9DE0D2] hover:bg-[#86C9BB] text-[#5D534B] transition-colors",children:n?"Simpan Perubahan":"Tambah Agenda"})]})]})})]})})};export{R as default};
