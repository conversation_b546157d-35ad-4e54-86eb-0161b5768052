var l=Object.defineProperty;var h=(o,t,i)=>t in o?l(o,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):o[t]=i;var c=(o,t,i)=>h(o,typeof t!="symbol"?t+"":t,i);import{w as f,E as d,P as n}from"./index-C9M2Ov7x.js";const e=f("PushNotifications",{});class g{constructor(){c(this,"isInitialized",!1);c(this,"currentToken",null);c(this,"notificationHistory",[])}async initialize(){if(this.isInitialized){console.log("ℹ️ Push notifications already initialized");return}if(!d.isNativePlatform()){console.log("ℹ️ Not a native platform, skipping push notification initialization"),this.isInitialized=!0;return}try{if(console.log("🔔 Starting push notification initialization..."),typeof e>"u")throw new Error("PushNotifications plugin not available");if(console.log("📋 Requesting permissions..."),(await this.requestPermissions()).receive!=="granted"){console.warn("⚠️ Push notification permissions not granted"),this.isInitialized=!0;return}console.log("📱 Registering for push notifications..."),await e.register(),console.log("👂 Setting up listeners..."),this.setupListeners(),this.isInitialized=!0,console.log("✅ Push notifications initialized successfully")}catch(t){console.error("❌ Failed to initialize push notifications:",t),this.isInitialized=!0}}async requestPermissions(){const t=await e.requestPermissions();return t.receive==="granted"?console.log("✅ Push notification permissions granted"):console.log("❌ Push notification permissions denied"),t}async checkPermissions(){return await e.checkPermissions()}setupListeners(){e.addListener("registration",async t=>{console.log("📱 Push registration success, token:",t.value),this.currentToken=t.value,await this.saveTokenToServer(t.value)}),e.addListener("registrationError",t=>{console.error("❌ Push registration error:",t)}),e.addListener("pushNotificationReceived",t=>{console.log("📨 Push notification received:",t),this.handleForegroundNotification(t)}),e.addListener("pushNotificationActionPerformed",t=>{console.log("👆 Push notification action performed:",t),this.handleNotificationAction(t)})}async handleForegroundNotification(t){const i={id:Date.now().toString(),title:t.title||"",body:t.body||"",data:t.data,receivedAt:new Date().toISOString(),isRead:!1};this.notificationHistory.unshift(i),await this.saveNotificationHistory(),this.showInAppNotification(t),await this.updateBadgeCount()}async handleNotificationAction(t){const{notification:i,actionId:s}=t,a=i.data,r=this.notificationHistory.find(u=>u.data===a);switch(r&&(r.isRead=!0,r.actionTaken=s,r.readAt=new Date().toISOString(),await this.saveNotificationHistory()),a?.type){case"chat_message":this.handleChatNotificationAction(a,s);break;case"announcement":this.handleAnnouncementAction(a,s);break;case"event_reminder":this.handleEventReminderAction(a,s);break;case"dues_reminder":this.handleDuesReminderAction(a,s);break}await this.updateBadgeCount()}handleChatNotificationAction(t,i){window.dispatchEvent(new CustomEvent("navigate-to-chat",{detail:{chatId:t.chatId,messageId:t.messageId}}))}handleAnnouncementAction(t,i){t.actionUrl?window.dispatchEvent(new CustomEvent("navigate-to-url",{detail:{url:t.actionUrl}})):window.dispatchEvent(new CustomEvent("navigate-to-announcements"))}handleEventReminderAction(t,i){window.dispatchEvent(new CustomEvent("navigate-to-events",{detail:{eventId:t.eventId}}))}handleDuesReminderAction(t,i){window.dispatchEvent(new CustomEvent("navigate-to-finance"))}showInAppNotification(t){const i=new CustomEvent("show-in-app-notification",{detail:{title:t.title,body:t.body,data:t.data}});window.dispatchEvent(i)}async getToken(){return this.currentToken}async saveTokenToServer(t){try{const i={token:t,platform:"android",deviceId:await this.getDeviceId(),createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()};await n.set({key:"push_token",value:JSON.stringify(i)}),console.log("💾 Token saved:",i)}catch(i){console.error("❌ Failed to save token:",i)}}async getDeviceId(){const{value:t}=await n.get({key:"device_id"});if(t)return t;const i=`android_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;return await n.set({key:"device_id",value:i}),i}async getNotificationSettings(){const{value:t}=await n.get({key:"notification_settings"});if(t)return JSON.parse(t);const i={chatMessages:!0,announcements:!0,eventReminders:!0,duesReminders:!0,sound:!0,vibration:!0,badge:!0,customSounds:{chatMessage:"default",announcement:"announcement",eventReminder:"reminder",duesReminder:"urgent"}};return await this.saveNotificationSettings(i),i}async saveNotificationSettings(t){await n.set({key:"notification_settings",value:JSON.stringify(t)})}async getNotificationHistory(){if(this.notificationHistory.length===0){const{value:t}=await n.get({key:"notification_history"});t&&(this.notificationHistory=JSON.parse(t))}return this.notificationHistory}async saveNotificationHistory(){this.notificationHistory.length>100&&(this.notificationHistory=this.notificationHistory.slice(0,100)),await n.set({key:"notification_history",value:JSON.stringify(this.notificationHistory)})}async getUnreadCount(){return(await this.getNotificationHistory()).filter(i=>!i.isRead).length}async markAllAsRead(){this.notificationHistory.forEach(t=>{t.isRead||(t.isRead=!0,t.readAt=new Date().toISOString())}),await this.saveNotificationHistory(),await this.updateBadgeCount()}async updateBadgeCount(){const t=await this.getUnreadCount();d.isNativePlatform()&&console.log(`📱 Badge count: ${t}`)}async clearNotificationHistory(){this.notificationHistory=[],await n.remove({key:"notification_history"})}async unregister(){d.isNativePlatform()&&await e.removeAllListeners(),this.isInitialized=!1,this.currentToken=null}}const w=new g;export{w as pushNotificationService};
