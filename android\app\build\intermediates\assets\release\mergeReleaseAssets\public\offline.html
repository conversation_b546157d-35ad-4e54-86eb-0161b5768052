<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - DANAPEMUDA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #F9F9F9;
            color: #5D534B;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .offline-container {
            text-align: center;
            max-width: 400px;
            background: white;
            padding: 40px 30px;
            border: 4px solid #5D534B;
            border-radius: 16px;
            box-shadow: 8px 8px 0px #5D534B;
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            background: #FF9898;
            border: 4px solid #5D534B;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
        }
        
        h1 {
            font-size: 24px;
            font-weight: 900;
            margin-bottom: 16px;
            color: #5D534B;
        }
        
        p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 24px;
            color: #5D534B;
        }
        
        .retry-btn {
            background: #FCE09B;
            color: #5D534B;
            border: 4px solid #5D534B;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 700;
            border-radius: 8px;
            cursor: pointer;
            box-shadow: 4px 4px 0px #5D534B;
            transition: all 0.2s;
        }
        
        .retry-btn:hover {
            box-shadow: 6px 6px 0px #5D534B;
            transform: translate(-2px, -2px);
        }
        
        .retry-btn:active {
            box-shadow: 2px 2px 0px #5D534B;
            transform: translate(2px, 2px);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            background: #9DE0D2;
            border: 2px solid #5D534B;
            border-radius: 4px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1>Anda Sedang Offline</h1>
        <p>Tidak dapat terhubung ke internet. Beberapa fitur masih dapat digunakan secara offline.</p>
        
        <button class="retry-btn" onclick="retryConnection()">
            Coba Lagi
        </button>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">💬</div>
                <span>Chat tersimpan secara lokal</span>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <span>Data anggota dapat dilihat</span>
            </div>
            <div class="feature">
                <div class="feature-icon">🔄</div>
                <span>Sinkronisasi otomatis saat online</span>
            </div>
        </div>
    </div>

    <script>
        function retryConnection() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                alert('Masih tidak ada koneksi internet. Silakan coba lagi nanti.');
            }
        }

        // Auto-retry when back online
        window.addEventListener('online', () => {
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        // Update UI based on connection status
        window.addEventListener('offline', () => {
            document.querySelector('.retry-btn').textContent = 'Tidak Ada Koneksi';
            document.querySelector('.retry-btn').disabled = true;
        });

        window.addEventListener('online', () => {
            document.querySelector('.retry-btn').textContent = 'Coba Lagi';
            document.querySelector('.retry-btn').disabled = false;
        });
    </script>
</body>
</html>
