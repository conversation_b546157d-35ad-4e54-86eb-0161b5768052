<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PMD\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PMD\android\app\src\main\res"><file name="ic_launcher_background" path="D:\PMD\android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable\splash.png" qualifiers="" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-land-hdpi\splash.png" qualifiers="land-hdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-land-mdpi\splash.png" qualifiers="land-mdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-land-xhdpi\splash.png" qualifiers="land-xhdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-land-xxhdpi\splash.png" qualifiers="land-xxhdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-land-xxxhdpi\splash.png" qualifiers="land-xxxhdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-port-hdpi\splash.png" qualifiers="port-hdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-port-mdpi\splash.png" qualifiers="port-mdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-port-xhdpi\splash.png" qualifiers="port-xhdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-port-xxhdpi\splash.png" qualifiers="port-xxhdpi-v4" type="drawable"/><file name="splash" path="D:\PMD\android\app\src\main\res\drawable-port-xxxhdpi\splash.png" qualifiers="port-xxxhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="D:\PMD\android\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_main" path="D:\PMD\android\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\PMD\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\PMD\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\PMD\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PMD\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PMD\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PMD\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PMD\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PMD\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PMD\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PMD\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PMD\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PMD\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PMD\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PMD\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PMD\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PMD\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PMD\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\PMD\android\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="D:\PMD\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">DANAPEMUDA</string><string name="title_activity_main">DANAPEMUDA</string><string name="package_name">com.danapemuda.app</string><string name="custom_url_scheme">com.danapemuda.app</string></file><file path="D:\PMD\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="AppTheme.NoActionBar" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:background">@null</item>
    </style><style name="AppTheme.NoActionBarLaunch" parent="Theme.SplashScreen">
        <item name="android:background">@drawable/splash</item>
    </style></file><file name="config" path="D:\PMD\android\app\src\main\res\xml\config.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\PMD\android\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\PMD\android\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PMD\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PMD\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PMD\android\app\build\generated\res\resValues\release"/><source path="D:\PMD\android\app\build\generated\res\processReleaseGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PMD\android\app\build\generated\res\resValues\release"/><source path="D:\PMD\android\app\build\generated\res\processReleaseGoogleServices"><file path="D:\PMD\android\app\build\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">792680197911</string><string name="google_api_key" translatable="false">AIzaSyBR75XOXl28IeElITca355njLgYSTA1AwE</string><string name="google_app_id" translatable="false">1:792680197911:android:5f18fba82d8f5321419152</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBR75XOXl28IeElITca355njLgYSTA1AwE</string><string name="google_storage_bucket" translatable="false">pemuda-psy.firebasestorage.app</string><string name="project_id" translatable="false">pemuda-psy</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>