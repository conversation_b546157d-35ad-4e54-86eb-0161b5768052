[{"merged": "com.danapemuda.app-debug-32:/drawable-land-xxhdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-land-xxhdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/xml_file_paths.xml.flat", "source": "com.danapemuda.app-main-34:/xml/file_paths.xml"}, {"merged": "com.danapemuda.app-debug-32:/drawable_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-mdpi_ic_launcher_round.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-mdpi/ic_launcher_round.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-port-xxhdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-port-xxhdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/xml_config.xml.flat", "source": "com.danapemuda.app-main-34:/xml/config.xml"}, {"merged": "com.danapemuda.app-debug-32:/drawable-land-xxxhdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-land-xxxhdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-mdpi_ic_launcher_background.png.flat", "source": "com.danapemuda.app-pngs-27:/drawable-mdpi/ic_launcher_background.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xxxhdpi_ic_launcher_foreground.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xxxhdpi/ic_launcher_foreground.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-ldpi_ic_launcher_background.png.flat", "source": "com.danapemuda.app-pngs-27:/drawable-ldpi/ic_launcher_background.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-land-xhdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-land-xhdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-xhdpi_ic_launcher_background.png.flat", "source": "com.danapemuda.app-pngs-27:/drawable-xhdpi/ic_launcher_background.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.danapemuda.app-main-34:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.danapemuda.app-debug-32:/drawable-port-xxxhdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-port-xxxhdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.danapemuda.app-main-34:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.danapemuda.app-debug-32:/layout_activity_main.xml.flat", "source": "com.danapemuda.app-main-34:/layout/activity_main.xml"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xhdpi_ic_launcher_foreground.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xhdpi/ic_launcher_foreground.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xxhdpi_ic_launcher_foreground.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xxhdpi/ic_launcher_foreground.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-anydpi-v24_ic_launcher_background.xml.flat", "source": "com.danapemuda.app-pngs-27:/drawable-anydpi-v24/ic_launcher_background.xml"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xxhdpi/ic_launcher_round.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-port-hdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-port-hdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-port-xhdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-port-xhdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-xxhdpi_ic_launcher_background.png.flat", "source": "com.danapemuda.app-pngs-27:/drawable-xxhdpi/ic_launcher_background.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-hdpi_ic_launcher_background.png.flat", "source": "com.danapemuda.app-pngs-27:/drawable-hdpi/ic_launcher_background.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xhdpi_ic_launcher_round.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xhdpi/ic_launcher_round.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-mdpi_ic_launcher_foreground.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-mdpi/ic_launcher_foreground.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-port-mdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-port-mdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-land-mdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-land-mdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-hdpi_ic_launcher_foreground.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-hdpi/ic_launcher_foreground.png"}, {"merged": "com.danapemuda.app-debug-32:/mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "com.danapemuda.app-main-34:/mipmap-xxxhdpi/ic_launcher_round.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-land-hdpi_splash.png.flat", "source": "com.danapemuda.app-main-34:/drawable-land-hdpi/splash.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-xxxhdpi_ic_launcher_background.png.flat", "source": "com.danapemuda.app-pngs-27:/drawable-xxxhdpi/ic_launcher_background.png"}, {"merged": "com.danapemuda.app-debug-32:/drawable-v24_ic_launcher_foreground.xml.flat", "source": "com.danapemuda.app-main-34:/drawable-v24/ic_launcher_foreground.xml"}, {"merged": "com.danapemuda.app-debug-32:/xml_network_security_config.xml.flat", "source": "com.danapemuda.app-main-34:/xml/network_security_config.xml"}]