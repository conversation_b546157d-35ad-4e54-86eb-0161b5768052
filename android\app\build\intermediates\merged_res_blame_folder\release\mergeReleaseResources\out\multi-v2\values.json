{"logs": [{"outputFile": "com.danapemuda.app-mergeReleaseResources-30:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a4193c6fbbe5e128015b7f6283124c0\\transformed\\fragment-1.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "261,268,291,2820,2825", "startColumns": "4,4,4,4,4", "startOffsets": "17569,17868,19077,163384,163554", "endLines": "261,268,291,2824,2828", "endColumns": "56,64,63,24,24", "endOffsets": "17621,17928,19136,163549,163698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5af023aa879967c1b3537c24628d5a4\\transformed\\activity-1.9.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "267,288", "startColumns": "4,4", "startOffsets": "17826,18913", "endColumns": "41,59", "endOffsets": "17863,18968"}}, {"source": "D:\\PMD\\android\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,572", "endColumns": "81,103,108,119,101,70", "endOffsets": "132,236,345,465,567,638"}, "to": {"startLines": "355,356,357,358,359,362", "startColumns": "4,4,4,4,4,4", "startOffsets": "24578,24660,24764,24873,24993,25235", "endColumns": "81,103,108,119,101,70", "endOffsets": "24655,24759,24868,24988,25090,25301"}}, {"source": "D:\\PMD\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "4,11,18", "startColumns": "4,4,4", "startOffsets": "93,413,664", "endLines": "9,15,20", "endColumns": "12,12,12", "endOffsets": "407,657,810"}, "to": {"startLines": "371,377,382", "startColumns": "4,4,4", "startOffsets": "25931,26215,26464", "endLines": "376,381,384", "endColumns": "12,12,12", "endOffsets": "26210,26459,26610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dda665aa4a1576cfb1759fb2bbcd5279\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,35,36,50,51,52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,183,184,185,186,187,188,189,190,191,214,215,216,217,218,219,220,221,257,258,259,260,262,265,266,269,286,292,293,294,295,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,363,366,367,368,369,370,385,393,394,398,402,406,411,417,424,428,432,437,441,445,449,453,457,461,467,471,477,481,487,491,496,500,503,507,513,517,523,527,533,536,540,544,548,552,556,557,558,559,562,565,568,571,575,576,577,578,579,582,584,586,588,593,594,598,604,608,609,611,623,624,628,634,638,642,643,647,674,678,679,683,711,883,909,1080,1106,1137,1145,1151,1167,1189,1194,1199,1209,1218,1227,1231,1238,1257,1264,1265,1274,1277,1280,1284,1288,1292,1295,1296,1301,1306,1316,1321,1328,1334,1335,1338,1342,1347,1349,1351,1354,1357,1359,1363,1366,1373,1376,1379,1383,1385,1389,1391,1393,1395,1399,1407,1415,1427,1433,1442,1445,1456,1459,1460,1465,1466,1475,1544,1614,1615,1625,1634,1635,1637,1641,1644,1647,1650,1653,1656,1659,1662,1666,1669,1672,1675,1679,1682,1686,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1712,1714,1715,1716,1717,1718,1719,1720,1721,1723,1724,1726,1727,1729,1731,1732,1734,1735,1736,1737,1738,1739,1741,1742,1743,1744,1745,1757,1759,1761,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1777,1778,1779,1780,1781,1782,1783,1785,1789,1819,1820,1821,1822,1823,1824,1828,1829,1830,1831,1833,1835,1837,1839,1841,1842,1843,1844,1846,1848,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1864,1865,1866,1867,1869,1871,1872,1874,1875,1877,1879,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1894,1895,1896,1897,1899,1900,1901,1902,1903,1905,1907,1909,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1939,2014,2017,2020,2023,2037,2043,2085,2088,2117,2144,2153,2217,2580,2590,2628,2746,2868,2892,2898,2917,2938,3062,3082,3088,3092,3098,3152,3184,3250,3270,3325,3337,3363", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "211,266,311,539,856,911,973,1037,1107,1168,1243,1319,1396,1634,1719,1801,1877,1953,2030,2108,2214,2320,2399,2479,2536,3627,3701,3776,3841,3907,3967,4028,4100,4173,4240,4365,4424,4483,4542,4601,4660,4714,4768,4821,4875,4929,4983,5169,5243,5322,5395,5469,5540,5612,5684,5757,5814,5872,5945,6019,6093,6168,6240,6313,6383,6454,6514,6575,6644,6713,6783,6857,6933,6997,7074,7150,7227,7292,7361,7438,7513,7582,7650,7727,7793,7854,7951,8016,8085,8184,8255,8314,8372,8429,8488,8552,8623,8695,8767,8839,8911,8978,9046,9114,9173,9236,9300,9390,9481,9541,9607,9674,9740,9810,9874,9927,9994,10055,10122,10235,10293,10356,10421,10486,10561,10634,10706,10750,10797,10843,10892,10953,11014,11075,11137,11201,11265,11329,11394,11457,11517,11578,11644,11703,11763,11825,11896,11956,12512,12598,12685,12775,12862,12950,13032,13115,13205,14788,14840,14898,14943,15009,15073,15130,15187,17364,17421,17469,17518,17626,17730,17777,17933,18838,19141,19205,19267,19327,19590,19664,19734,19812,19866,19936,20021,20069,20115,20176,20239,20305,20369,20440,20503,20568,20632,20693,20754,20806,20879,20953,21022,21097,21171,21245,21386,25306,25489,25567,25657,25745,25841,26615,27197,27286,27533,27814,28066,28351,28744,29221,29443,29665,29941,30168,30398,30628,30858,31088,31315,31734,31960,32385,32615,33043,33262,33545,33753,33884,34111,34537,34762,35189,35410,35835,35955,36231,36532,36856,37147,37461,37598,37729,37834,38076,38243,38447,38655,38926,39038,39150,39255,39372,39586,39732,39872,39958,40306,40394,40640,41058,41307,41389,41487,42144,42244,42496,42920,43175,43535,43624,43861,45885,46127,46229,46482,48638,59319,60835,71530,73058,74815,75441,75861,77122,78387,78643,78879,79426,79920,80525,80723,81303,82671,83046,83164,83702,83859,84055,84328,84584,84754,84895,84959,85324,85691,86367,86631,86969,87322,87416,87602,87908,88170,88295,88422,88661,88872,88991,89184,89361,89816,89997,90119,90378,90491,90678,90780,90887,91016,91291,91799,92295,93172,93466,94036,94185,94917,95089,95173,95509,95601,96109,101340,106711,106773,107351,107935,108026,108139,108368,108528,108680,108851,109017,109186,109353,109516,109759,109929,110102,110273,110547,110746,110951,111281,111365,111461,111557,111655,111755,111857,111959,112061,112163,112265,112365,112461,112573,112702,112825,112956,113087,113185,113299,113393,113533,113667,113763,113875,113975,114091,114187,114299,114399,114539,114675,114839,114969,115127,115277,115418,115562,115697,115809,115959,116087,116215,116351,116483,116613,116743,116855,117753,117899,118043,118181,118247,118337,118413,118517,118607,118709,118817,118925,119025,119105,119197,119295,119405,119457,119535,119641,119733,119837,119947,120069,120232,121897,121977,122077,122167,122277,122367,122608,122702,122808,122900,123000,123112,123226,123342,123458,123552,123666,123778,123880,124000,124122,124204,124308,124428,124554,124652,124746,124834,124946,125062,125184,125296,125471,125587,125673,125765,125877,126001,126068,126194,126262,126390,126534,126662,126731,126826,126941,127054,127153,127262,127373,127484,127585,127690,127790,127920,128011,128134,128228,128340,128426,128530,128626,128714,128832,128936,129040,129166,129254,129362,129462,129552,129662,129746,129848,129932,129986,130050,130156,130242,130352,130436,131238,133854,133972,134087,134167,134528,134761,136165,136243,137587,138948,139336,142179,152232,152570,154241,160325,164552,165303,165565,166080,166459,170737,171343,171572,171723,171938,173438,174288,177314,178058,180189,180529,181840", "endLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,35,36,50,51,52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,183,184,185,186,187,188,189,190,191,214,215,216,217,218,219,220,221,257,258,259,260,262,265,266,269,286,292,293,294,295,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,363,366,367,368,369,370,392,393,397,401,405,410,416,423,427,431,436,440,444,448,452,456,460,466,470,476,480,486,490,495,499,502,506,512,516,522,526,532,535,539,543,547,551,555,556,557,558,561,564,567,570,574,575,576,577,578,581,583,585,587,592,593,597,603,607,608,610,622,623,627,633,637,638,642,646,673,677,678,682,710,882,908,1079,1105,1136,1144,1150,1166,1188,1193,1198,1208,1217,1226,1230,1237,1256,1263,1264,1273,1276,1279,1283,1287,1291,1294,1295,1300,1305,1315,1320,1327,1333,1334,1337,1341,1346,1348,1350,1353,1356,1358,1362,1365,1372,1375,1378,1382,1384,1388,1390,1392,1394,1398,1406,1414,1426,1432,1441,1444,1455,1458,1459,1464,1465,1470,1543,1613,1614,1624,1633,1634,1636,1640,1643,1646,1649,1652,1655,1658,1661,1665,1668,1671,1674,1678,1681,1685,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1711,1713,1714,1715,1716,1717,1718,1719,1720,1722,1723,1725,1726,1728,1730,1731,1733,1734,1735,1736,1737,1738,1740,1741,1742,1743,1744,1745,1758,1760,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1776,1777,1778,1779,1780,1781,1782,1784,1788,1792,1819,1820,1821,1822,1823,1827,1828,1829,1830,1832,1834,1836,1838,1840,1841,1842,1843,1845,1847,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1863,1864,1865,1866,1868,1870,1871,1873,1874,1876,1878,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1893,1894,1895,1896,1898,1899,1900,1901,1902,1904,1906,1908,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,2013,2016,2019,2022,2036,2042,2052,2087,2116,2143,2152,2216,2579,2583,2617,2655,2763,2891,2897,2903,2937,3061,3081,3087,3091,3097,3132,3163,3249,3269,3324,3336,3362,3369", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "261,306,355,575,906,968,1032,1102,1163,1238,1314,1391,1469,1714,1796,1872,1948,2025,2103,2209,2315,2394,2474,2531,2589,3696,3771,3836,3902,3962,4023,4095,4168,4235,4303,4419,4478,4537,4596,4655,4709,4763,4816,4870,4924,4978,5032,5238,5317,5390,5464,5535,5607,5679,5752,5809,5867,5940,6014,6088,6163,6235,6308,6378,6449,6509,6570,6639,6708,6778,6852,6928,6992,7069,7145,7222,7287,7356,7433,7508,7577,7645,7722,7788,7849,7946,8011,8080,8179,8250,8309,8367,8424,8483,8547,8618,8690,8762,8834,8906,8973,9041,9109,9168,9231,9295,9385,9476,9536,9602,9669,9735,9805,9869,9922,9989,10050,10117,10230,10288,10351,10416,10481,10556,10629,10701,10745,10792,10838,10887,10948,11009,11070,11132,11196,11260,11324,11389,11452,11512,11573,11639,11698,11758,11820,11891,11951,12019,12593,12680,12770,12857,12945,13027,13110,13200,13291,14835,14893,14938,15004,15068,15125,15182,15236,17416,17464,17513,17564,17655,17772,17821,17974,18865,19200,19262,19322,19379,19659,19729,19807,19861,19931,20016,20064,20110,20171,20234,20300,20364,20435,20498,20563,20627,20688,20749,20801,20874,20948,21017,21092,21166,21240,21381,21451,25354,25562,25652,25740,25836,25926,27192,27281,27528,27809,28061,28346,28739,29216,29438,29660,29936,30163,30393,30623,30853,31083,31310,31729,31955,32380,32610,33038,33257,33540,33748,33879,34106,34532,34757,35184,35405,35830,35950,36226,36527,36851,37142,37456,37593,37724,37829,38071,38238,38442,38650,38921,39033,39145,39250,39367,39581,39727,39867,39953,40301,40389,40635,41053,41302,41384,41482,42139,42239,42491,42915,43170,43264,43619,43856,45880,46122,46224,46477,48633,59314,60830,71525,73053,74810,75436,75856,77117,78382,78638,78874,79421,79915,80520,80718,81298,82666,83041,83159,83697,83854,84050,84323,84579,84749,84890,84954,85319,85686,86362,86626,86964,87317,87411,87597,87903,88165,88290,88417,88656,88867,88986,89179,89356,89811,89992,90114,90373,90486,90673,90775,90882,91011,91286,91794,92290,93167,93461,94031,94180,94912,95084,95168,95504,95596,95874,101335,106706,106768,107346,107930,108021,108134,108363,108523,108675,108846,109012,109181,109348,109511,109754,109924,110097,110268,110542,110741,110946,111276,111360,111456,111552,111650,111750,111852,111954,112056,112158,112260,112360,112456,112568,112697,112820,112951,113082,113180,113294,113388,113528,113662,113758,113870,113970,114086,114182,114294,114394,114534,114670,114834,114964,115122,115272,115413,115557,115692,115804,115954,116082,116210,116346,116478,116608,116738,116850,116990,117894,118038,118176,118242,118332,118408,118512,118602,118704,118812,118920,119020,119100,119192,119290,119400,119452,119530,119636,119728,119832,119942,120064,120227,120384,121972,122072,122162,122272,122362,122603,122697,122803,122895,122995,123107,123221,123337,123453,123547,123661,123773,123875,123995,124117,124199,124303,124423,124549,124647,124741,124829,124941,125057,125179,125291,125466,125582,125668,125760,125872,125996,126063,126189,126257,126385,126529,126657,126726,126821,126936,127049,127148,127257,127368,127479,127580,127685,127785,127915,128006,128129,128223,128335,128421,128525,128621,128709,128827,128931,129035,129161,129249,129357,129457,129547,129657,129741,129843,129927,129981,130045,130151,130237,130347,130431,130551,133849,133967,134082,134162,134523,134756,135273,136238,137582,138943,139331,142174,152227,152362,153935,155593,160892,165298,165560,165760,166454,170732,171338,171567,171718,171933,173016,173745,177309,178053,180184,180524,181835,182038"}}, {"source": "D:\\PMD\\node_modules\\@capacitor\\splash-screen\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2,4,7", "startColumns": "4,4,4", "startOffsets": "55,141,289", "endLines": "3,6,9", "endColumns": "12,12,12", "endOffsets": "136,284,448"}, "to": {"startLines": "1931,1933,1936", "startColumns": "4,4,4", "startOffsets": "130840,130926,131074", "endLines": "1932,1935,1938", "endColumns": "12,12,12", "endOffsets": "130921,131069,131233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bf10fd02607fac4185e6132b9261c407\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "297,343", "startColumns": "4,4", "startOffsets": "19452,23138", "endColumns": "67,166", "endOffsets": "19515,23300"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef234481c09f01fb9f0508a5da2b1126\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "7,8,10,11,12,13,207,208,209,210,211,212,213,296,639,640,641,1471,1473,1793,1802,1815", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "420,480,580,649,721,784,14274,14348,14424,14500,14577,14648,14717,19384,43269,43350,43442,95879,95988,120389,120849,121624", "endLines": "7,8,10,11,12,13,207,208,209,210,211,212,213,296,639,640,641,1472,1474,1801,1814,1818", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "475,534,644,716,779,851,14343,14419,14495,14572,14643,14712,14783,19447,43345,43437,43530,95983,96104,120844,121619,121892"}}, {"source": "D:\\PMD\\node_modules\\@capacitor\\android\\capacitor\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "100,175,251,331", "endColumns": "74,75,79,79", "endOffsets": "170,246,326,406"}, "to": {"startLines": "39,40,41,360", "startColumns": "4,4,4,4", "startOffsets": "2725,2800,2876,25095", "endColumns": "74,75,79,79", "endOffsets": "2795,2871,2951,25170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\998c918bf96ae2f6a4f5c8c644413a6f\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "289", "startColumns": "4", "startOffsets": "18973", "endColumns": "53", "endOffsets": "19022"}}, {"source": "D:\\PMD\\android\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "4308", "endColumns": "56", "endOffsets": "4360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5a75dca28172537968edb11f4713fc67\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "290", "startColumns": "4", "startOffsets": "19027", "endColumns": "49", "endOffsets": "19072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16884767c054ac4cab0f70a5a4855d4d\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2053,2069,2075,3164,3180", "startColumns": "4,4,4,4,4", "startOffsets": "135278,135703,135881,173750,174161", "endLines": "2068,2074,2084,3179,3183", "endColumns": "24,24,24,24,24", "endOffsets": "135698,135876,136160,174156,174283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f212eb2fcec7b76a8049b85cea08416b\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "287", "startColumns": "4", "startOffsets": "18870", "endColumns": "42", "endOffsets": "18908"}}, {"source": "D:\\PMD\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,5,4,3", "startColumns": "4,4,4,4", "startOffsets": "55,222,162,103", "endColumns": "47,64,59,58", "endOffsets": "98,282,217,157"}, "to": {"startLines": "327,353,361,365", "startColumns": "4,4,4,4", "startOffsets": "21539,24431,25175,25430", "endColumns": "47,64,59,58", "endOffsets": "21582,24491,25230,25484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c04d43294058e70b8ad79d5184e7401\\transformed\\firebase-messaging-24.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "24496", "endColumns": "81", "endOffsets": "24573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0c35e3b0c2fe34519a603108fedf6f64\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "326", "startColumns": "4", "startOffsets": "21456", "endColumns": "82", "endOffsets": "21534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\75881b531e34911967ea794bd3408c30\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "2,1928,2656,2662", "startColumns": "4,4,4,4", "startOffsets": "150,130695,155598,155809", "endLines": "2,1930,2661,2745", "endColumns": "60,12,24,24", "endOffsets": "206,130835,155804,160320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f51ed623ec66baebfa6a053fe8a8b2a\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "6,23,24,37,38,73,74,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,263,264,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,298,328,329,330,331,332,333,334,364,1746,1747,1751,1752,1756,1926,1927,2584,2618,2764,2799,2829,2862", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "360,1474,1546,2594,2659,5037,5106,12024,12094,12162,12234,12304,12365,12439,13296,13357,13418,13480,13544,13606,13667,13735,13835,13895,13961,14034,14103,14160,14212,15241,15313,15389,15454,15513,15572,15632,15692,15752,15812,15872,15932,15992,16052,16112,16172,16231,16291,16351,16411,16471,16531,16591,16651,16711,16771,16831,16890,16950,17010,17069,17128,17187,17246,17305,17660,17695,17979,18034,18097,18152,18210,18268,18329,18392,18449,18500,18550,18611,18668,18734,18768,18803,19520,21587,21654,21726,21795,21864,21938,22010,25359,116995,117112,117313,117423,117624,130556,130628,152367,153940,160897,162703,163703,164385", "endLines": "6,23,24,37,38,73,74,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,263,264,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,298,328,329,330,331,332,333,334,364,1746,1750,1751,1755,1756,1926,1927,2589,2627,2798,2819,2861,2867", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "415,1541,1629,2654,2720,5101,5164,12089,12157,12229,12299,12360,12434,12507,13352,13413,13475,13539,13601,13662,13730,13830,13890,13956,14029,14098,14155,14207,14269,15308,15384,15449,15508,15567,15627,15687,15747,15807,15867,15927,15987,16047,16107,16167,16226,16286,16346,16406,16466,16526,16586,16646,16706,16766,16826,16885,16945,17005,17064,17123,17182,17241,17300,17359,17690,17725,18029,18092,18147,18205,18263,18324,18387,18444,18495,18545,18606,18663,18729,18763,18798,18833,19585,21649,21721,21790,21859,21933,22005,22093,25425,117107,117308,117418,117619,117748,130623,130690,152565,154236,162698,163379,164380,164547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b230ccc4c3071da754e2bd5ef15689db\\transformed\\play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "42,43,44,45,46,47,48,49,335,336,337,338,339,340,341,342,344,345,346,347,348,349,350,351,352,2904,3133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2956,3046,3126,3216,3306,3386,3467,3547,22098,22203,22384,22509,22616,22796,22919,23035,23305,23493,23598,23779,23904,24079,24227,24290,24352,165765,173021", "endLines": "42,43,44,45,46,47,48,49,335,336,337,338,339,340,341,342,344,345,346,347,348,349,350,351,352,2916,3151", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "3041,3121,3211,3301,3381,3462,3542,3622,22198,22379,22504,22611,22791,22914,23030,23133,23488,23593,23774,23899,24074,24222,24285,24347,24426,166075,173433"}}]}]}