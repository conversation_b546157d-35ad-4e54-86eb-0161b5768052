<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="ic_launcher_background">#FFFFFF</color>
    <string name="app_name">DANAPEMUDA</string>
    <string name="custom_url_scheme">com.danapemuda.app</string>
    <string name="gcm_defaultSenderId" translatable="false">792680197911</string>
    <string name="google_api_key" translatable="false">AIzaSyBR75XOXl28IeElITca355njLgYSTA1AwE</string>
    <string name="google_app_id" translatable="false">1:792680197911:android:5f18fba82d8f5321419152</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBR75XOXl28IeElITca355njLgYSTA1AwE</string>
    <string name="google_storage_bucket" translatable="false">pemuda-psy.firebasestorage.app</string>
    <string name="package_name">com.danapemuda.app</string>
    <string name="project_id" translatable="false">pemuda-psy</string>
    <string name="title_activity_main">DANAPEMUDA</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:background">@null</item>
    </style>
    <style name="AppTheme.NoActionBarLaunch" parent="Theme.SplashScreen">
        <item name="android:background">@drawable/splash</item>
    </style>
</resources>