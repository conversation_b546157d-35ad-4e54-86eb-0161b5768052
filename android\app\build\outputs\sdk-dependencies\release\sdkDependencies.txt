# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.9.2"
  }
  digests {
    sha256: "ru\036X\225\266\375`\017\257>\275\375<\233/\257p\350\321{A<\vf\332\331\214\246K*w"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.25"
  }
  digests {
    sha256: "\371\315\315\277\361\365\336\2058\n\345&\227~h7&\302\252B\333\036\326\346\345\n\350\236In\225\375"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.25"
  }
  digests {
    sha256: "\371O\337x9\f\351\27608;\3609\305\2515\312\3523\261\037\003\177\307\370k\274\356\031(~Z"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.25"
  }
  digests {
    sha256: "\373Ss\335v\033N\223\343\3658\305\350S\273\243\212q\024:\030\0256\350\361\223\355nN\335\263\270"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.25"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.15.0"
  }
  digests {
    sha256: "C+\205\241\227@v\341KH~\316J(\305\232\204\361\271\357\303\374\213\347,\327\360]2\005^Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.4"
  }
  digests {
    sha256: "?\334\016\355[\304\270>\351b\'tR\n-\262Tp7\016\254\321X\034\254\0367pO\t[\000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.4"
  }
  digests {
    sha256: "\302L\213\262{\263 \304\2518qP\032~^\fa`v8\220{\031z\357gU\023\324\310 \276"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.6.4"
  }
  digests {
    sha256: "N\343xL4e\312\253\206\255\314O\223\225mV\352f*\254\202\345\302\310<\243\301\360A_\343\227"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.1.0"
  }
  digests {
    sha256: "\326\005u\352\343\223P\346#HX\274\235}wSupz\350*hNl\257\177>A\241.%\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.3.0"
  }
  digests {
    sha256: "l\021\256>\262\335\177\0277?\221\234LUzp\344\317\211\033\300\311\266i&\240\246D]eCR"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.8.4"
  }
  digests {
    sha256: ":z\257\025qs\366P\325}\222]/\005\201\251\235Wv\3602\352\306\370;^\205\274\3704@\351"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.15.0"
  }
  digests {
    sha256: "\307,\227\377J20\213\312\363\006\036\352]\3373E\\\344\023\316\344\252\035\\\202D\fWY\324\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.2.0"
  }
  digests {
    sha256: "w\224\b\261\2523\f\324\247\255\361\262\350_\322\211\303\016~\335.\216B\204{}\006X\232\367\025\372"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-splashscreen"
    version: "1.0.1"
  }
  digests {
    sha256: "%\310\023\256w\225\311\235\322\006(RpC\370\320B^\262\350&\346\304,\267z\313\266\300\035\336h"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.9.25"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.12.1"
  }
  digests {
    sha256: "\030\240\245\032\321)\340\310V?\003\035F\310\247\346\021i\365j\350NV*\ruhiA&!\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.apache.cordova"
    artifactId: "framework"
    version: "10.1.1"
  }
  digests {
    sha256: "\334k~~\344+sc\202\233\231\237q\227R*\2403\230\252\314\245\247\341\372\302Z^?y3\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.1.0"
  }
  digests {
    sha256: "\325\357>\352\027 \214\362\024\375\3541\037}\034G\216\321\301\2349\243\260c\023\361\022\210\307#\004\331"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.1"
  }
  digests {
    sha256: "(\226\327oC+\345!g)[\271\316E\255\342\\1\n\357\374\004\322\214\370\333j\025\206\216\203\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 42
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 28
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 34
  library_dep_index: 38
  library_dep_index: 49
  library_dep_index: 36
  library_dep_index: 4
  library_dep_index: 42
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 36
  library_dep_index: 33
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 4
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 4
}
library_dependencies {
  library_index: 8
  library_dep_index: 4
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
}
library_dependencies {
  library_index: 10
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 11
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 9
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 4
  library_dep_index: 28
}
library_dependencies {
  library_index: 12
  library_dep_index: 4
}
library_dependencies {
  library_index: 13
  library_dep_index: 2
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 2
}
library_dependencies {
  library_index: 16
  library_dep_index: 2
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 38
  library_dep_index: 4
  library_dep_index: 19
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 30
}
library_dependencies {
  library_index: 17
  library_dep_index: 2
}
library_dependencies {
  library_index: 18
  library_dep_index: 2
  library_dep_index: 17
}
library_dependencies {
  library_index: 19
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 16
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 6
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 22
}
library_dependencies {
  library_index: 24
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 6
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 9
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 36
  library_dep_index: 39
  library_dep_index: 4
}
library_dependencies {
  library_index: 28
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 4
  library_dep_index: 11
}
library_dependencies {
  library_index: 29
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 4
  library_dep_index: 19
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 16
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 30
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 4
  library_dep_index: 19
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 16
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 31
  library_dep_index: 2
  library_dep_index: 16
  library_dep_index: 32
  library_dep_index: 4
  library_dep_index: 19
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 32
  library_dep_index: 2
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 2
}
library_dependencies {
  library_index: 34
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 19
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 16
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 2
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 16
  library_dep_index: 34
}
library_dependencies {
  library_index: 36
  library_dep_index: 2
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 4
}
library_dependencies {
  library_index: 37
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 30
  library_dep_index: 34
}
library_dependencies {
  library_index: 38
  library_dep_index: 2
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 14
}
library_dependencies {
  library_index: 39
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 2
  library_dep_index: 11
}
library_dependencies {
  library_index: 41
  library_dep_index: 2
  library_dep_index: 9
}
library_dependencies {
  library_index: 42
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 0
}
library_dependencies {
  library_index: 43
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 9
}
library_dependencies {
  library_index: 44
  library_dep_index: 43
  library_dep_index: 15
  library_dep_index: 9
}
library_dependencies {
  library_index: 45
  library_dep_index: 2
}
library_dependencies {
  library_index: 46
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 40
}
library_dependencies {
  library_index: 47
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 47
  library_dep_index: 47
}
library_dependencies {
  library_index: 49
  library_dep_index: 2
}
library_dependencies {
  library_index: 50
  library_dep_index: 9
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 40
}
library_dependencies {
  library_index: 51
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 52
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 53
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 11
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
  library_dep_index: 61
  library_dep_index: 57
  library_dep_index: 62
  library_dep_index: 65
  library_dep_index: 68
  library_dep_index: 66
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 2
  library_dep_index: 63
  library_dep_index: 67
  library_dep_index: 64
  library_dep_index: 73
  library_dep_index: 26
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 25
  library_dep_index: 60
  library_dep_index: 4
}
library_dependencies {
  library_index: 56
  library_dep_index: 24
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 2
  library_dep_index: 13
  library_dep_index: 4
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
  library_dep_index: 2
  library_dep_index: 60
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 61
  library_dep_index: 56
  library_dep_index: 6
  library_dep_index: 57
  library_dep_index: 58
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 67
  library_dep_index: 2
}
library_dependencies {
  library_index: 63
  library_dep_index: 2
}
library_dependencies {
  library_index: 64
  library_dep_index: 63
  library_dep_index: 2
  library_dep_index: 59
  library_dep_index: 65
  library_dep_index: 66
}
library_dependencies {
  library_index: 65
  library_dep_index: 2
}
library_dependencies {
  library_index: 66
  library_dep_index: 2
  library_dep_index: 65
}
library_dependencies {
  library_index: 67
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 65
  library_dep_index: 68
  library_dep_index: 2
}
library_dependencies {
  library_index: 68
  library_dep_index: 2
  library_dep_index: 65
}
library_dependencies {
  library_index: 69
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
  library_dep_index: 4
  library_dep_index: 25
  library_dep_index: 58
  library_dep_index: 56
  library_dep_index: 61
  library_dep_index: 57
}
library_dependencies {
  library_index: 71
  library_dep_index: 25
  library_dep_index: 58
}
library_dependencies {
  library_index: 72
  library_dep_index: 26
  library_dep_index: 58
}
library_dependencies {
  library_index: 73
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 74
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
  library_dep_index: 26
}
library_dependencies {
  library_index: 76
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 77
  library_dep_index: 37
  library_dep_index: 78
  library_dep_index: 79
}
library_dependencies {
  library_index: 77
  library_dep_index: 2
}
library_dependencies {
  library_index: 78
  library_dep_index: 2
}
library_dependencies {
  library_index: 79
  library_dep_index: 2
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 50
  dependency_index: 51
  dependency_index: 52
  dependency_index: 11
  dependency_index: 1
  dependency_index: 27
  dependency_index: 53
  dependency_index: 54
  dependency_index: 55
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
