import{f as se,r as i,j as e,g as te,m as l,c as L,B as n,A as K,X as O,C as R,J as w}from"./index-H1RsCKxW.js";import{f as D,a as q}from"./formatters-LGS2Cxr7.js";import{I as h}from"./input-CExfTrNt.js";import{C as o,a as p}from"./card-ur2eNWbw.js";import{w as J,h as B}from"./errorHandler-aLQZqrtb.js";import{C as re,F as f}from"./file-text-BiCOyrIO.js";import{P as _}from"./plus-Ci2WE_St.js";import{S as ne,T as M,P as C}from"./tag-Dr-E4cFH.js";import{S as y}from"./save-dq9uI7Kz.js";import{T as U}from"./trash-2-CRs-iYtX.js";const F=["Operasional","Konsumsi","Transport","Sound System","Lainnya"],ge=()=>{const{expenses:E,loading:V,addExpense:H,updateExpense:X,deleteExpense:G}=se(),k=a=>!a||a===0?"":new Intl.NumberFormat("id-ID").format(a),P=a=>{if(!a)return 0;const r=a.replace(/[^\d]/g,"");return parseInt(r)||0},[S,b]=i.useState(!1),[Y,N]=i.useState(!1),[T,v]=i.useState(null),[c,Q]=i.useState(""),[x,W]=i.useState(""),[s,j]=i.useState({description:"",amount:0,date:new Date().toISOString().split("T")[0],category:""}),[t,m]=i.useState({description:"",amount:0,date:new Date().toISOString().split("T")[0],category:""}),[d,u]=i.useState(!1),g=i.useMemo(()=>E.filter(a=>{const r=a.description.toLowerCase().includes(c.toLowerCase())||a.category?.toLowerCase().includes(c.toLowerCase()),ae=!x||a.category===x;return r&&ae}),[E,c,x]),Z=i.useMemo(()=>g.reduce((a,r)=>a+r.amount,0),[g]),$=async()=>{try{u(!0),await J(()=>H({description:s.description,amount:s.amount,date:s.date,category:s.category,type:"expense",createdBy:"admin"}),3,1e3,"Add Expense"),w.success("✅ Pengeluaran berhasil ditambahkan"),j({description:"",amount:0,date:new Date().toISOString().split("T")[0],category:""}),b(!1)}catch(a){B(a,"Add Expense")}finally{u(!1)}},I=a=>{m({description:a.description,amount:a.amount,date:a.date,category:a.category||""}),v(a.id),N(!0)},ee=async()=>{if(T)try{u(!0),await J(()=>X(T,{description:t.description,amount:t.amount,date:t.date,category:t.category}),3,1e3,"Update Expense"),w.success("✅ Pengeluaran berhasil diperbarui"),m({description:"",amount:0,date:new Date().toISOString().split("T")[0],category:""}),N(!1),v(null)}catch(a){B(a,"Update Expense")}finally{u(!1)}},A=()=>{N(!1),v(null),m({description:"",amount:0,date:new Date().toISOString().split("T")[0],category:""})},z=async a=>{if(window.confirm("Yakin ingin menghapus pengeluaran ini?"))try{u(!0),await G(a),w.success("✅ Pengeluaran berhasil dihapus")}catch(r){B(r,"Delete Expense")}finally{u(!1)}};return V?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(te,{size:"medium",text:"Memuat Data Pengeluaran..."})}):e.jsxs("div",{className:"space-y-6 p-4 sm:p-6",children:[e.jsxs(l.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-3 bg-gradient-to-br from-red-100 to-orange-100 rounded-xl",children:e.jsx(L,{className:"w-8 h-8 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-[#5D534B]",children:"Kelola Pengeluaran"}),e.jsx("p",{className:"text-[#5D534B] opacity-70 text-sm sm:text-base",children:"Pantau dan kelola semua pengeluaran organisasi"})]})]}),e.jsxs(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[e.jsx(o,{className:"neo-card bg-[#FF9898] border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B]",children:e.jsx(p,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#5D534B] text-sm font-medium",children:"Total Pengeluaran"}),e.jsx("p",{className:"text-2xl font-bold text-[#5D534B]",children:D(Z)})]}),e.jsx("div",{className:"p-3 bg-[#5D534B] border-2 border-[#5D534B] shadow-[2px_2px_0px_rgba(0,0,0,0.3)]",children:e.jsx("div",{className:"w-6 h-6 flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"Rp"})})})]})})}),e.jsx(o,{className:"neo-card bg-[#B39DDB] border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B]",children:e.jsx(p,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#5D534B] text-sm font-medium",children:"Jumlah Transaksi"}),e.jsx("p",{className:"text-2xl font-bold text-[#5D534B]",children:g.length})]}),e.jsx("div",{className:"p-3 bg-[#5D534B] border-2 border-[#5D534B] shadow-[2px_2px_0px_rgba(0,0,0,0.3)]",children:e.jsx(re,{className:"w-6 h-6 text-white"})})]})})})]}),e.jsx(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.15},className:"flex justify-center",children:e.jsxs(n,{type:"button",onClick:()=>b(!0),className:"bg-[#FF9898] text-[#5D534B] px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-bold flex items-center space-x-2 border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] hover:bg-[#FF8A8A] transition-all duration-200 rounded-lg",disabled:S,children:[e.jsx(_,{size:18,className:"sm:w-5 sm:h-5"}),e.jsx("span",{className:"hidden sm:inline",children:"Tambah Pengeluaran Baru"}),e.jsx("span",{className:"sm:hidden",children:"Tambah Pengeluaran"})]})}),e.jsx(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:e.jsx(o,{className:"neo-card border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B]",children:e.jsx(p,{className:"p-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ne,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-[#5D534B] w-5 h-5"}),e.jsx(h,{placeholder:"Cari pengeluaran...",value:c,onChange:a=>Q(a.target.value),className:"pl-10 h-12 border-2 border-[#5D534B] focus:border-[#FF9898] focus:ring-[#FF9898] shadow-[2px_2px_0px_#5D534B] font-medium"})]})}),e.jsx("div",{className:"sm:w-48",children:e.jsxs("select",{value:x,onChange:a=>W(a.target.value),className:"w-full h-12 px-3 border-2 border-[#5D534B] rounded-lg focus:border-[#FF9898] focus:ring-[#FF9898] bg-white shadow-[2px_2px_0px_#5D534B] font-medium",title:"Filter berdasarkan kategori","aria-label":"Filter berdasarkan kategori",children:[e.jsx("option",{value:"",children:"Semua Kategori"}),F.map(a=>e.jsx("option",{value:a,children:a},a))]})})]})})})}),e.jsx(K,{children:S&&e.jsx(l.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:e.jsx(o,{className:"neo-card bg-gradient-to-br from-red-50 to-orange-50 border-red-200",children:e.jsxs(p,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:e.jsx(_,{className:"w-6 h-6 text-red-600"})}),e.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"Tambah Pengeluaran Baru"})]}),e.jsx(n,{type:"button",onClick:()=>b(!1),className:"p-2 hover:bg-red-100 rounded-lg transition-colors",variant:"ghost",children:e.jsx(O,{className:"w-5 h-5 text-gray-500"})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"description",className:"flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2",children:[e.jsx(f,{className:"w-4 h-4"}),e.jsx("span",{children:"Deskripsi Pengeluaran"})]}),e.jsx(h,{id:"description",value:s.description,onChange:a=>j({...s,description:a.target.value}),className:"h-12 border-gray-300 focus:border-red-500 focus:ring-red-500",placeholder:"Contoh: Pembelian ATK untuk acara",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"amount",className:"flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2",children:[e.jsx("div",{className:"w-4 h-4 flex items-center justify-center",children:e.jsx("span",{className:"text-gray-700 font-bold text-xs",children:"Rp"})}),e.jsx("span",{children:"Jumlah Pengeluaran"})]}),e.jsx(h,{id:"amount",type:"text",value:k(s.amount),onChange:a=>{const r=P(a.target.value);j({...s,amount:r})},className:"h-12 border-gray-300 focus:border-red-500 focus:ring-red-500",placeholder:"Contoh: 50.000",required:!0})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"date",className:"flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2",children:[e.jsx(R,{className:"w-4 h-4"}),e.jsx("span",{children:"Tanggal Pengeluaran"})]}),e.jsx(h,{id:"date",type:"date",value:s.date,onChange:a=>j({...s,date:a.target.value}),className:"h-12 border-gray-300 focus:border-red-500 focus:ring-red-500",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"category",className:"flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2",children:[e.jsx(M,{className:"w-4 h-4"}),e.jsx("span",{children:"Kategori"})]}),e.jsxs("select",{id:"category",value:s.category,onChange:a=>j({...s,category:a.target.value}),className:"w-full h-12 px-3 border border-gray-300 rounded-lg focus:border-red-500 focus:ring-red-500 bg-white",children:[e.jsx("option",{value:"",children:"Pilih kategori"}),F.map(a=>e.jsx("option",{value:a,children:a},a))]})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-8",children:[e.jsx(n,{type:"button",onClick:()=>b(!1),className:"px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-semibold transition-colors",variant:"ghost",children:"Batal"}),e.jsx(n,{type:"button",onClick:$,disabled:!s.description||s.amount<=0||!s.date||d,className:"px-6 py-3 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:d?e.jsxs(e.Fragment,{children:[e.jsx(y,{size:18,className:"mr-2 animate-spin"}),"Menyimpan..."]}):e.jsxs(e.Fragment,{children:[e.jsx(y,{size:18,className:"mr-2"}),"Simpan Pengeluaran"]})})]})]})})})}),e.jsx(K,{children:Y&&e.jsx(l.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:e.jsx(o,{className:"neo-card bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200",children:e.jsxs(p,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:e.jsx(C,{className:"w-6 h-6 text-blue-600"})}),e.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"Edit Pengeluaran"})]}),e.jsx(n,{type:"button",onClick:A,className:"p-2 hover:bg-blue-100 rounded-lg transition-colors",variant:"ghost",children:e.jsx(O,{className:"w-5 h-5 text-gray-500"})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"edit-description",className:"flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2",children:[e.jsx(f,{className:"w-4 h-4"}),e.jsx("span",{children:"Deskripsi Pengeluaran"})]}),e.jsx(h,{id:"edit-description",value:t.description,onChange:a=>m({...t,description:a.target.value}),className:"h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500",placeholder:"Contoh: Pembelian ATK untuk acara",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"edit-amount",className:"flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2",children:[e.jsx("div",{className:"w-4 h-4 flex items-center justify-center",children:e.jsx("span",{className:"text-gray-700 font-bold text-xs",children:"Rp"})}),e.jsx("span",{children:"Jumlah Pengeluaran"})]}),e.jsx(h,{id:"edit-amount",type:"text",value:k(t.amount),onChange:a=>{const r=P(a.target.value);m({...t,amount:r})},className:"h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500",placeholder:"Contoh: 50.000",required:!0})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"edit-date",className:"flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2",children:[e.jsx(R,{className:"w-4 h-4"}),e.jsx("span",{children:"Tanggal Pengeluaran"})]}),e.jsx(h,{id:"edit-date",type:"date",value:t.date,onChange:a=>m({...t,date:a.target.value}),className:"h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"edit-category",className:"flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2",children:[e.jsx(M,{className:"w-4 h-4"}),e.jsx("span",{children:"Kategori"})]}),e.jsxs("select",{id:"edit-category",value:t.category,onChange:a=>m({...t,category:a.target.value}),className:"w-full h-12 px-3 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-blue-500 bg-white",children:[e.jsx("option",{value:"",children:"Pilih kategori"}),F.map(a=>e.jsx("option",{value:a,children:a},a))]})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-8",children:[e.jsx(n,{type:"button",onClick:A,className:"px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-semibold transition-colors",variant:"ghost",children:"Batal"}),e.jsx(n,{type:"button",onClick:ee,disabled:!t.description||t.amount<=0||!t.date||d,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:d?e.jsxs(e.Fragment,{children:[e.jsx(y,{size:18,className:"mr-2 animate-spin"}),"Memperbarui..."]}):e.jsxs(e.Fragment,{children:[e.jsx(y,{size:18,className:"mr-2"}),"Perbarui Pengeluaran"]})})]})]})})})}),e.jsx(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:g.length===0?e.jsx(o,{className:"neo-card",children:e.jsx(p,{className:"p-12 text-center",children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"p-4 bg-gray-100 rounded-full",children:e.jsx(L,{className:"w-12 h-12 text-gray-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:c||x?"Tidak ada hasil":"Belum ada pengeluaran"}),e.jsx("p",{className:"text-gray-600",children:c||x?"Coba ubah filter pencarian Anda":"Tambah pengeluaran pertama untuk mulai melacak keuangan"})]}),!c&&!x&&e.jsxs(n,{type:"button",onClick:()=>b(!0),className:"bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-xl font-semibold",children:[e.jsx(_,{size:18,className:"mr-2"}),"Tambah Pengeluaran Pertama"]})]})})}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"hidden lg:block",children:e.jsx(o,{className:"neo-card border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-[#FF9898] border-b-2 border-[#5D534B]",children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-left py-4 px-6 font-bold text-[#5D534B]",children:"Deskripsi"}),e.jsx("th",{className:"text-left py-4 px-6 font-bold text-[#5D534B]",children:"Tanggal"}),e.jsx("th",{className:"text-left py-4 px-6 font-bold text-[#5D534B]",children:"Kategori"}),e.jsx("th",{className:"text-right py-4 px-6 font-bold text-[#5D534B]",children:"Jumlah"}),e.jsx("th",{className:"text-center py-4 px-6 font-bold text-[#5D534B]",children:"Aksi"})]})}),e.jsx("tbody",{children:g.map((a,r)=>e.jsxs(l.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:r*.05},className:"border-b-2 border-[#5D534B] hover:bg-[#FF9898]/20 transition-colors",children:[e.jsx("td",{className:"py-4 px-6",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-[#FF9898] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded-lg",children:e.jsx(f,{className:"w-4 h-4 text-[#5D534B]"})}),e.jsx("span",{className:"font-medium text-[#5D534B]",children:a.description})]})}),e.jsx("td",{className:"py-4 px-6 text-[#5D534B] font-medium",children:q(a.date)}),e.jsx("td",{className:"py-4 px-6",children:e.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#B39DDB] text-[#5D534B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B]",children:a.category||"Lainnya"})}),e.jsx("td",{className:"py-4 px-6 text-right",children:e.jsx("span",{className:"text-lg font-bold text-[#5D534B]",children:D(a.amount)})}),e.jsx("td",{className:"py-4 px-6 text-center",children:e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(n,{type:"button",onClick:()=>I(a),className:"p-2 bg-blue-100 hover:bg-blue-200 text-blue-600 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] rounded-lg transition-all",disabled:d,title:"Edit pengeluaran",variant:"ghost",children:e.jsx(C,{size:16})}),e.jsx(n,{type:"button",onClick:()=>z(a.id),className:"p-2 bg-red-100 hover:bg-red-200 text-red-600 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] rounded-lg transition-all",disabled:d,title:"Hapus pengeluaran",variant:"ghost",children:e.jsx(U,{size:16})})]})})]},a.id))})]})})})}),e.jsx("div",{className:"lg:hidden space-y-4",children:g.map((a,r)=>e.jsx(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:r*.1},children:e.jsx(o,{className:"neo-card border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] transition-all",children:e.jsxs(p,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3 flex-1",children:[e.jsx("div",{className:"p-2 bg-[#FF9898] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded-lg",children:e.jsx(f,{className:"w-5 h-5 text-[#5D534B]"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-[#5D534B] truncate",children:a.description}),e.jsx("p",{className:"text-sm text-[#5D534B] font-medium",children:q(a.date)})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-2",children:[e.jsx(n,{type:"button",onClick:()=>I(a),className:"p-2 bg-blue-100 hover:bg-blue-200 text-blue-600 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] rounded-lg transition-all",disabled:d,title:"Edit pengeluaran",variant:"ghost",children:e.jsx(C,{size:16})}),e.jsx(n,{type:"button",onClick:()=>z(a.id),className:"p-2 bg-red-100 hover:bg-red-200 text-red-600 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] rounded-lg transition-all",disabled:d,title:"Hapus pengeluaran",variant:"ghost",children:e.jsx(U,{size:16})})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#B39DDB] text-[#5D534B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B]",children:a.category||"Lainnya"}),e.jsx("span",{className:"text-lg font-bold text-[#5D534B]",children:D(a.amount)})]})]})})},a.id))})]})})]})};export{ge as default};
