import{u as B,f as E,r as s,j as e,g as F,U as w,c as l,C as S}from"./index-rvmMexdh.js";import{S as o}from"./StatCard-B9QiAxax.js";import{f as r,a as T}from"./formatters-LGS2Cxr7.js";import{h as k}from"./errorHandler-Izs8RmeO.js";import{C as L}from"./credit-card-CeVONm4c.js";const R=()=>{const{members:i,loading:d}=B(),{expenses:c,getTotalExpenses:m,loading:x}=E(),[g,b]=s.useState({totalIncome:0,totalExpense:0,balance:0}),[f,h]=s.useState(0),[p,j]=s.useState(0),[N,u]=s.useState(!0),n=s.useCallback(async()=>{try{u(!0);const a=i.reduce((v,C)=>v+C.payment_amount,0),t=m(),D={totalIncome:a,totalExpense:t,balance:a-t},y=a-t;b(D),h(a),j(y)}catch(a){k(a,"FinancePage: load financial data")}finally{u(!1)}},[i,m]);return s.useEffect(()=>{n()},[n]),s.useEffect(()=>{!d&&!x&&n()},[d,x,n]),N?e.jsx("div",{className:"flex flex-col items-center justify-center min-h-[50vh]",children:e.jsx(F,{size:"large",variant:"secondary",text:"Memuat Data Keuangan..."})}):e.jsxs("div",{className:"bg-[#F9F9F9] text-[#5D534B] rounded-2xl",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h1",{className:"text-2xl md:text-3xl font-bold border-b-4 border-[#FF9898] pb-2",children:"Keuangan"})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsx(o,{title:"Total Iuran Anggota",value:r(f),icon:w,gradientClass:"neo-gradient-blue",borderColor:"border-neo-blue"}),e.jsx(o,{title:"Total Pengeluaran",value:r(g.totalExpense),icon:l,gradientClass:"neo-gradient-pink",borderColor:"border-neo-pink"}),e.jsx(o,{title:"Sisa Saldo",value:r(p),icon:L,gradientClass:"neo-gradient-yellow",borderColor:"border-neo-yellow"})]}),e.jsxs("div",{className:"neo-card p-4 overflow-hidden animate-fade-in bg-white border-4 border-[#9DE0D2]",children:[e.jsxs("h2",{className:"text-xl font-bold mb-4 flex items-center text-[#5D534B]",children:[e.jsx(l,{className:"mr-2",size:20}),"Transaksi Pengeluaran"]}),e.jsx("div",{className:"space-y-4",children:c.length>0?c.sort((a,t)=>new Date(t.date).getTime()-new Date(a.date).getTime()).map(a=>e.jsx("div",{className:"expense-card bg-white p-4 border-2 border-[#5D534B] rounded-lg shadow-sm",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold text-[#5D534B]",children:a.description}),e.jsxs("div",{className:"flex items-center text-sm mt-1 text-[#5D534B]",children:[e.jsx(S,{size:14,className:"mr-1"}),e.jsx("span",{children:T(a.date)})]}),a.category&&e.jsx("div",{className:"mt-1",children:e.jsx("span",{className:"inline-block px-2 py-1 text-xs border border-[#5D534B] rounded-full bg-[#FCE09B] text-[#5D534B]",children:a.category})})]}),e.jsx("div",{children:e.jsx("span",{className:"text-md font-bold px-3 py-1 border-2 border-[#5D534B] rounded-full bg-[#FF9898] text-[#5D534B]",children:r(a.amount)})})]})},a.id)):e.jsxs("div",{className:"text-center py-8 text-[#5D534B]",children:[e.jsx(l,{size:48,className:"mx-auto mb-4 text-[#FF9898]"}),e.jsx("p",{className:"text-lg font-medium",children:"Belum ada transaksi pengeluaran"}),e.jsx("p",{className:"text-sm opacity-70",children:"Pengeluaran akan muncul di sini setelah ditambahkan"})]})})]})]})};export{R as default};
