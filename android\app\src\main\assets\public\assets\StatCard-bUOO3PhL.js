import{j as e}from"./index-BIYh9seP.js";const n=({title:s,value:t,icon:a,gradientClass:i="neo-gradient-pink",borderColor:r="border-neo-pink"})=>e.jsx("div",{className:`neo-card p-4 ${i} animate-fade-in ${r} transition-all duration-200`,children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-bold mb-1 text-[#5D534B]",children:s}),e.jsx("p",{className:"text-xl md:text-2xl font-bold text-[#5D534B]",children:t})]}),e.jsx("div",{className:"bg-white p-2 border-4 border-[#5D534B] rounded-full shadow-pastel-sm",children:e.jsx(a,{size:20,strokeWidth:2.5,className:"text-[#5D534B]"})})]})});export{n as S};
