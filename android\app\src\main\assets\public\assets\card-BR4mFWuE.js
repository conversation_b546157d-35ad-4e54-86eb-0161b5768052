import{r as s,j as d,l as t}from"./index-yTxXUAod.js";const o=s.forwardRef(({className:a,...e},r)=>d.jsx("div",{ref:r,className:t("rounded-lg border bg-card text-card-foreground shadow-sm",a),...e}));o.displayName="Card";const c=s.forwardRef(({className:a,...e},r)=>d.jsx("div",{ref:r,className:t("flex flex-col space-y-1.5 p-6",a),...e}));c.displayName="CardHeader";const i=s.forwardRef(({className:a,...e},r)=>d.jsx("h3",{ref:r,className:t("text-2xl font-semibold leading-none tracking-tight",a),...e}));i.displayName="CardTitle";const l=s.forwardRef(({className:a,...e},r)=>d.jsx("p",{ref:r,className:t("text-sm text-muted-foreground",a),...e}));l.displayName="CardDescription";const n=s.forwardRef(({className:a,...e},r)=>d.jsx("div",{ref:r,className:t("p-6 pt-0",a),...e}));n.displayName="CardContent";const m=s.forwardRef(({className:a,...e},r)=>d.jsx("div",{ref:r,className:t("flex items-center p-6 pt-0",a),...e}));m.displayName="CardFooter";export{o as C,n as a,c as b,i as c};
