const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-DE6BxYZW.js","assets/index-D9lfh6x2.js","assets/index-DEfeMmQh.css"])))=>i.map(i=>d[i]);
import{w as t,_ as n}from"./index-D9lfh6x2.js";var e;(function(r){r.Heavy="HEAVY",r.Medium="MEDIUM",r.Light="LIGHT"})(e||(e={}));var i;(function(r){r.Success="SUCCESS",r.Warning="WARNING",r.Error="ERROR"})(i||(i={}));const a=t("Haptics",{web:()=>n(()=>import("./web-DE6BxYZW.js"),__vite__mapDeps([0,1,2])).then(r=>new r.HapticsWeb)});export{a as Haptics,e as ImpactStyle,i as NotificationType};
