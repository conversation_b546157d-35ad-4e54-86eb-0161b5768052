const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-CGgrthqc.js","assets/index-C9M2Ov7x.js","assets/index-B1dvnxZO.css"])))=>i.map(i=>d[i]);
import{w as t,_ as n}from"./index-C9M2Ov7x.js";var e;(function(r){r.Heavy="HEAVY",r.Medium="MEDIUM",r.Light="LIGHT"})(e||(e={}));var i;(function(r){r.Success="SUCCESS",r.Warning="WARNING",r.Error="ERROR"})(i||(i={}));const a=t("Haptics",{web:()=>n(()=>import("./web-CGgrthqc.js"),__vite__mapDeps([0,1,2])).then(r=>new r.HapticsWeb)});export{a as Haptics,e as ImpactStyle,i as NotificationType};
