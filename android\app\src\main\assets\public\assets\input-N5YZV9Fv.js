import{r as s,j as i,l as n}from"./index-yTxXUAod.js";const l=s.forwardRef(({className:e,type:r,...o},t)=>i.jsx("input",{type:r,className:n("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...o}));l.displayName="Input";export{l as I};
