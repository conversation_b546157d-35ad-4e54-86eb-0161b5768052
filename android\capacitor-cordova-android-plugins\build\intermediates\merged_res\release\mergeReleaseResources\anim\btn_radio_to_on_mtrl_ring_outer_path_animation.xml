<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<set xmlns:android="http://schemas.android.com/apk/res/android">
    <set android:ordering="sequentially">
        <objectAnimator
                android:duration="166"
                android:propertyName="strokeWidth"
                android:valueFrom="2.0"
                android:valueTo="18.0"
                android:valueType="floatType"
                android:interpolator="@interpolator/btn_radio_to_on_mtrl_animation_interpolator_0"/>
        <objectAnimator
                android:duration="16"
                android:propertyName="strokeWidth"
                android:valueFrom="18.0"
                android:valueTo="2.0"
                android:valueType="floatType"
                android:interpolator="@interpolator/fast_out_slow_in"/>
        <objectAnimator
                android:duration="316"
                android:propertyName="strokeWidth"
                android:valueFrom="2.0"
                android:valueTo="2.0"
                android:valueType="floatType"
                android:interpolator="@interpolator/fast_out_slow_in"/>
    </set>
</set>
