[{"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_hint_foreground_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_hint_foreground_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_primary_text_disable_only_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_primary_text_disable_only_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_secondary_text_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_secondary_text_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_primary_text_disable_only_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_primary_text_disable_only_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_background_cache_hint_selector_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_background_cache_hint_selector_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_secondary_text_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_secondary_text_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_primary_text_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_primary_text_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_hint_foreground_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_hint_foreground_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_search_url_text.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_search_url_text.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/switch_thumb_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/switch_thumb_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_background_cache_hint_selector_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_background_cache_hint_selector_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/abc_primary_text_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/abc_primary_text_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/color/switch_thumb_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/color/switch_thumb_material_light.xml"}]