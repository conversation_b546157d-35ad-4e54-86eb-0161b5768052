[{"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_action_mode_bar.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_action_mode_bar.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/ime_secondary_split_test_activity.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.13.0-2:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/support_simple_spinner_dropdown_item.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/support_simple_spinner_dropdown_item.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_expanded_menu_layout.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_expanded_menu_layout.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_screen_simple.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_screen_simple.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_screen_toolbar.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_screen_toolbar.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_action_bar_up_container.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_action_bar_up_container.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_dialog_title_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_dialog_title_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/notification_template_part_time.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.13.0-2:/layout/notification_template_part_time.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_action_bar_title_item.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_action_bar_title_item.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_action_menu_layout.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_action_menu_layout.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_screen_simple_overlay_action_mode.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_screen_simple_overlay_action_mode.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/custom_dialog.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.13.0-2:/layout/custom_dialog.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_action_mode_close_item_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_action_mode_close_item_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_action_menu_item_layout.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_action_menu_item_layout.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_cascading_menu_item_layout.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_cascading_menu_item_layout.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_alert_dialog_title_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_alert_dialog_title_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_popup_menu_header_item_layout.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_popup_menu_header_item_layout.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_popup_menu_item_layout.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_popup_menu_item_layout.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_screen_content_include.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_screen_content_include.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_select_dialog_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_select_dialog_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/ime_base_split_test_activity.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.13.0-2:/layout/ime_base_split_test_activity.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/notification_template_part_chronometer.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.13.0-2:/layout/notification_template_part_chronometer.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_activity_chooser_view.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_activity_chooser_view.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/select_dialog_item_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/select_dialog_item_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_list_menu_item_checkbox.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_list_menu_item_checkbox.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/select_dialog_singlechoice_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/select_dialog_singlechoice_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_search_dropdown_item_icons_2line.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_search_dropdown_item_icons_2line.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_tooltip.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_tooltip.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/select_dialog_multichoice_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/select_dialog_multichoice_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_alert_dialog_button_bar_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_alert_dialog_button_bar_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_list_menu_item_radio.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_list_menu_item_radio.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_list_menu_item_layout.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_list_menu_item_layout.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_alert_dialog_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_alert_dialog_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_search_view.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_search_view.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_list_menu_item_icon.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_list_menu_item_icon.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-release-22:/layout/abc_activity_chooser_view_list_item.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.7.0-15:/layout/abc_activity_chooser_view_list_item.xml"}]