{"name": "dana-pemuda-pesayangan", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "npm run clean-console && vite build && npm run bundle-analyzer", "build:dev": "vite build --mode development", "build:analyze": "npm run build && npm run bundle-analyzer", "lint": "eslint .", "preview": "vite preview", "validate-env": "node scripts/validate-env.js", "clean-console": "node scripts/clean-console.js", "performance-check": "node scripts/performance-monitor.js", "bundle-analyzer": "node scripts/bundle-analyzer.js", "predev": "npm run validate-env", "build:mobile": "vite build --mode mobile", "emulator": "node scripts/run-emulator.mjs", "android:quick": "node scripts/quick-android.mjs", "android:dev": "scripts\\run-android.bat", "android:build": "npm run build:mobile && npx cap sync android && npx cap build android", "android:install": "powershell -ExecutionPolicy Bypass -File scripts/run-android.ps1 -Action install", "android:setup": "powershell -ExecutionPolicy Bypass -File scripts/setup-android-env.ps1", "test:push": "node scripts/test-push-notifications.mjs", "cap:add": "npx cap add", "cap:sync": "npx cap sync", "cap:open": "npx cap open", "cap:run": "npx cap run", "android:open": "npx cap open android", "android:release": "npm run build:mobile && npx cap sync android && cd android && ./gradlew assembleRelease", "mobile:init": "npm run build && npx cap add android && npx cap sync android", "mobile:setup": "npm run mobile:init && npm run android:open"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/app": "^7.0.1", "@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@capacitor/device": "^7.0.1", "@capacitor/haptics": "^7.0.1", "@capacitor/keyboard": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "esbuild": "^0.25.5", "file-saver": "^2.0.5", "firebase": "^11.10.0", "framer-motion": "^12.7.2", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "xlsx": "^0.18.5", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.29.0", "@stagewise-plugins/react": "^0.4.8", "@stagewise/toolbar-react": "^0.4.8", "@tailwindcss/typography": "^0.5.15", "@types/file-saver": "^2.0.7", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "axios": "^1.8.4", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "terser": "^5.43.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vite": "^6.3.5"}}