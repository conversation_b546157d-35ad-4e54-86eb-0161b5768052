#!/usr/bin/env node

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

console.log('🚀 Quick Android Development Setup\n');

async function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const { stdout, stderr } = await execAsync(command);
    if (stdout) console.log(stdout);
    if (stderr) console.log(stderr);
    console.log(`✅ ${description} completed!\n`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    throw error;
  }
}

async function checkEmulator() {
  try {
    const { stdout } = await execAsync('adb devices');
    const devices = stdout.split('\n').filter(line => 
      line.includes('emulator') && line.includes('device')
    );
    return devices.length > 0;
  } catch (error) {
    return false;
  }
}

async function main() {
  try {
    // Check if emulator is running
    console.log('🔍 Checking emulator status...');
    const isEmulatorRunning = await checkEmulator();
    
    if (!isEmulatorRunning) {
      console.log('❌ No emulator detected!');
      console.log('\n📱 Please start an emulator first:');
      console.log('1. Run: npm run emulator');
      console.log('2. Or open Android Studio → AVD Manager → Start emulator');
      console.log('3. Wait for emulator to fully boot');
      console.log('4. Then run this script again\n');
      process.exit(1);
    }
    
    console.log('✅ Emulator is running!\n');
    
    // Build and deploy
    await runCommand('npm run build:mobile', 'Building mobile app');
    await runCommand('npx cap sync android', 'Syncing with Capacitor');
    await runCommand('npx cap run android', 'Installing and running on Android');
    
    console.log('🎉 DANAPEMUDA app is now running on your Android emulator!');
    console.log('📱 Check your emulator to see the app');
    
  } catch (error) {
    console.error('\n❌ Quick setup failed:', error.message);
    console.log('\n🔧 Try manual steps:');
    console.log('1. npm run build:mobile');
    console.log('2. npx cap sync android');
    console.log('3. npx cap run android');
  }
}

main();
