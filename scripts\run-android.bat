@echo off
echo Setting up Android environment...

REM Set JAVA_HOME
set "JAVA_HOME=C:\Program Files\Android\Android Studio\jbr"
set "PATH=%JAVA_HOME%\bin;%PATH%"

REM Set ANDROID_HOME
set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
set "ANDROID_SDK_ROOT=%ANDROID_HOME%"
set "PATH=%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools;%PATH%"

echo Checking Java...
java -version
if %ERRORLEVEL% neq 0 (
    echo Java not found!
    pause
    exit /b 1
)

echo Checking ADB...
adb version
if %ERRORLEVEL% neq 0 (
    echo ADB not found!
    pause
    exit /b 1
)

echo Checking devices...
adb devices

echo Building mobile app...
call npm run build:mobile
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Syncing Capacitor...
call npx cap sync android
if %ERRORLEVEL% neq 0 (
    echo Sync failed!
    pause
    exit /b 1
)

echo Running app on Android...
call npx cap run android --target emulator-5554

pause
