# Run DANAPEMUDA Android App
param(
    [string]$Action = "run"
)

Write-Host "📱 DANAPEMUDA Android Runner" -ForegroundColor Green
Write-Host "Action: $Action" -ForegroundColor Cyan

# Setup environment first
Write-Host "`n🔧 Setting up environment..." -ForegroundColor Yellow
& "$PSScriptRoot\setup-android-env.ps1"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Environment setup failed" -ForegroundColor Red
    exit 1
}

# Function to run command with error handling
function Invoke-SafeCommand {
    param(
        [string]$Command,
        [string]$Description
    )
    
    Write-Host "`n📋 $Description..." -ForegroundColor Cyan
    Write-Host "Running: $Command" -ForegroundColor Gray
    
    try {
        Invoke-Expression $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $Description failed with error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Check if emulator is running
Write-Host "`n📱 Checking emulator status..." -ForegroundColor Cyan
$devices = adb devices 2>&1
$deviceLines = $devices -split "`n" | Where-Object { $_ -match "emulator.*device$" }

if ($deviceLines.Count -eq 0) {
    Write-Host "⚠️  No emulator detected. Starting emulator..." -ForegroundColor Yellow
    
    # Try to start emulator
    $avdList = & "$env:ANDROID_HOME\emulator\emulator.exe" -list-avds 2>&1
    if ($avdList -and $avdList.Count -gt 0) {
        $firstAvd = ($avdList | Where-Object { $_.Trim() -ne "" })[0]
        Write-Host "🚀 Starting emulator: $firstAvd" -ForegroundColor Green
        
        Start-Process -FilePath "$env:ANDROID_HOME\emulator\emulator.exe" -ArgumentList "-avd", $firstAvd, "-no-snapshot-save", "-no-snapshot-load" -NoNewWindow
        
        Write-Host "⏳ Waiting for emulator to start..." -ForegroundColor Yellow
        $timeout = 60
        $elapsed = 0
        
        do {
            Start-Sleep -Seconds 2
            $elapsed += 2
            $devices = adb devices 2>&1
            $deviceLines = $devices -split "`n" | Where-Object { $_ -match "emulator.*device$" }
            Write-Host "." -NoNewline -ForegroundColor Gray
        } while ($deviceLines.Count -eq 0 -and $elapsed -lt $timeout)
        
        Write-Host ""
        
        if ($deviceLines.Count -gt 0) {
            Write-Host "✅ Emulator started successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Emulator failed to start within $timeout seconds" -ForegroundColor Red
            Write-Host "Please start emulator manually and try again" -ForegroundColor Yellow
            exit 1
        }
    } else {
        Write-Host "❌ No AVDs found. Please create an AVD in Android Studio" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ Emulator is running: $($deviceLines[0])" -ForegroundColor Green
}

# Execute the requested action
switch ($Action.ToLower()) {
    "build" {
        if (-not (Invoke-SafeCommand "npm run build:mobile" "Building mobile app")) {
            exit 1
        }
    }
    
    "sync" {
        if (-not (Invoke-SafeCommand "npx cap sync android" "Syncing Capacitor")) {
            exit 1
        }
    }
    
    "run" {
        # Build, sync, and run
        if (-not (Invoke-SafeCommand "npm run build:mobile" "Building mobile app")) {
            exit 1
        }
        
        if (-not (Invoke-SafeCommand "npx cap sync android" "Syncing Capacitor")) {
            exit 1
        }
        
        Write-Host "`n🚀 Installing and running app..." -ForegroundColor Green
        npx cap run android --target emulator-5554
    }
    
    "install" {
        Write-Host "`n📦 Installing app to emulator..." -ForegroundColor Green
        npx cap run android --target emulator-5554
    }
    
    default {
        Write-Host "❌ Unknown action: $Action" -ForegroundColor Red
        Write-Host "Available actions: build, sync, run, install" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "`n🎉 Android operation completed!" -ForegroundColor Green
