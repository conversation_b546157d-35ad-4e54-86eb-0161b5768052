#!/usr/bin/env node

import { spawn, exec } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Starting Android Emulator for DANAPEMUDA...\n');

// Function to find Android SDK path
function findAndroidSdk() {
  const possiblePaths = [
    process.env.ANDROID_HOME,
    process.env.ANDROID_SDK_ROOT,
    path.join(process.env.LOCALAPPDATA || '', 'Android', 'Sdk'),
    path.join(process.env.HOME || '', 'Android', 'Sdk'),
    `C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk`
  ];

  for (const sdkPath of possiblePaths) {
    if (sdkPath && fs.existsSync(sdkPath)) {
      console.log(`✅ Found Android SDK at: ${sdkPath}`);
      return sdkPath;
    }
  }

  console.error('❌ Android SDK not found. Please install Android Studio or set ANDROID_HOME environment variable.');
  process.exit(1);
}

// Function to list available AVDs
function listAvds(sdkPath) {
  return new Promise((resolve, reject) => {
    const emulatorPath = path.join(sdkPath, 'emulator', 'emulator.exe');
    
    exec(`"${emulatorPath}" -list-avds`, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Error listing AVDs:', error.message);
        reject(error);
        return;
      }

      const avds = stdout.trim().split('\n').filter(line => line.trim());
      resolve(avds);
    });
  });
}

// Function to create a new AVD if none exists
function createAvd(sdkPath) {
  return new Promise((resolve, reject) => {
    console.log('📱 Creating new Android Virtual Device...');
    
    const avdManagerPath = path.join(sdkPath, 'cmdline-tools', 'latest', 'bin', 'avdmanager.bat');
    const avdName = 'DANAPEMUDA_Emulator';
    
    // Create AVD with Pixel 4 API 30
    const createCmd = `"${avdManagerPath}" create avd -n ${avdName} -k "system-images;android-30;google_apis;x86_64" -d "pixel_4"`;
    
    exec(createCmd, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Error creating AVD:', error.message);
        reject(error);
        return;
      }

      console.log('✅ AVD created successfully!');
      resolve(avdName);
    });
  });
}

// Function to start emulator
function startEmulator(sdkPath, avdName) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Starting emulator: ${avdName}...`);
    
    const emulatorPath = path.join(sdkPath, 'emulator', 'emulator.exe');
    
    const emulatorProcess = spawn(emulatorPath, [
      '-avd', avdName,
      '-no-snapshot-save',
      '-no-snapshot-load',
      '-gpu', 'host',
      '-skin', '1080x2340',
      '-memory', '2048'
    ], {
      stdio: 'inherit',
      detached: false
    });

    emulatorProcess.on('error', (error) => {
      console.error('❌ Error starting emulator:', error.message);
      reject(error);
    });

    emulatorProcess.on('close', (code) => {
      console.log(`📱 Emulator closed with code: ${code}`);
      resolve();
    });

    // Wait a bit for emulator to start
    setTimeout(() => {
      console.log('✅ Emulator is starting...');
      console.log('📱 You can now run: npm run android:dev');
      resolve();
    }, 5000);
  });
}

// Function to install app to emulator
function installApp() {
  return new Promise((resolve, reject) => {
    console.log('📦 Installing DANAPEMUDA app to emulator...');
    
    exec('npx cap run android', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Error installing app:', error.message);
        reject(error);
        return;
      }

      console.log('✅ App installed and launched successfully!');
      console.log(stdout);
      resolve();
    });
  });
}

// Main function
async function main() {
  try {
    const sdkPath = findAndroidSdk();
    
    console.log('📋 Checking available Android Virtual Devices...');
    let avds = await listAvds(sdkPath);
    
    if (avds.length === 0) {
      console.log('📱 No AVDs found. Creating new one...');
      const newAvd = await createAvd(sdkPath);
      avds = [newAvd];
    }

    console.log(`📱 Available AVDs: ${avds.join(', ')}`);
    const selectedAvd = avds[0]; // Use first available AVD

    await startEmulator(sdkPath, selectedAvd);
    
    // Ask user if they want to install the app
    console.log('\n🤔 Do you want to install and run DANAPEMUDA app? (y/n)');
    
    process.stdin.setEncoding('utf8');
    process.stdin.on('data', async (data) => {
      const input = data.toString().trim().toLowerCase();
      
      if (input === 'y' || input === 'yes') {
        try {
          await installApp();
        } catch (error) {
          console.error('❌ Failed to install app:', error.message);
        }
      }
      
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n👋 Emulator script terminated.');
  process.exit(0);
});

main();
