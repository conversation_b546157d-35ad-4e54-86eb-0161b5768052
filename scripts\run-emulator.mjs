#!/usr/bin/env node

import { spawn, exec } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Starting Android Emulator for DANAPEMUDA...\n');

// Function to find Android SDK path
function findAndroidSdk() {
  const possiblePaths = [
    process.env.ANDROID_HOME,
    process.env.ANDROID_SDK_ROOT,
    path.join(process.env.LOCALAPPDATA || '', 'Android', 'Sdk'),
    path.join(process.env.HOME || '', 'Android', 'Sdk'),
    `C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk`
  ];

  for (const sdkPath of possiblePaths) {
    if (sdkPath && fs.existsSync(sdkPath)) {
      console.log(`✅ Found Android SDK at: ${sdkPath}`);
      return sdkPath;
    }
  }

  console.error('❌ Android SDK not found. Please install Android Studio or set ANDROID_HOME environment variable.');
  console.log('\n📋 To fix this:');
  console.log('1. Install Android Studio from: https://developer.android.com/studio');
  console.log('2. Or set ANDROID_HOME environment variable to your SDK path');
  console.log('3. Make sure Android SDK is properly installed');
  process.exit(1);
}

// Function to list available AVDs
function listAvds(sdkPath) {
  return new Promise((resolve, reject) => {
    const emulatorPath = path.join(sdkPath, 'emulator', 'emulator.exe');
    
    if (!fs.existsSync(emulatorPath)) {
      console.error(`❌ Emulator not found at: ${emulatorPath}`);
      reject(new Error('Emulator executable not found'));
      return;
    }
    
    exec(`"${emulatorPath}" -list-avds`, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Error listing AVDs:', error.message);
        reject(error);
        return;
      }

      const avds = stdout.trim().split('\n').filter(line => line.trim());
      resolve(avds);
    });
  });
}

// Function to start emulator
function startEmulator(sdkPath, avdName) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Starting emulator: ${avdName}...`);
    
    const emulatorPath = path.join(sdkPath, 'emulator', 'emulator.exe');
    
    const emulatorProcess = spawn(emulatorPath, [
      '-avd', avdName,
      '-no-snapshot-save',
      '-no-snapshot-load',
      '-gpu', 'host',
      '-memory', '2048'
    ], {
      stdio: 'inherit',
      detached: false
    });

    emulatorProcess.on('error', (error) => {
      console.error('❌ Error starting emulator:', error.message);
      reject(error);
    });

    emulatorProcess.on('close', (code) => {
      console.log(`📱 Emulator closed with code: ${code}`);
      resolve();
    });

    // Wait a bit for emulator to start
    setTimeout(() => {
      console.log('✅ Emulator is starting...');
      console.log('📱 Wait for emulator to fully boot, then run: npm run android:dev');
      resolve();
    }, 5000);
  });
}

// Function to check if emulator is running
function checkEmulatorRunning() {
  return new Promise((resolve) => {
    exec('adb devices', (error, stdout, stderr) => {
      if (error) {
        resolve(false);
        return;
      }
      
      const devices = stdout.split('\n').filter(line => 
        line.includes('emulator') && line.includes('device')
      );
      
      resolve(devices.length > 0);
    });
  });
}

// Main function
async function main() {
  try {
    console.log('🔍 Checking if emulator is already running...');
    const isRunning = await checkEmulatorRunning();
    
    if (isRunning) {
      console.log('✅ Emulator is already running!');
      console.log('📱 You can run: npm run android:dev');
      return;
    }

    const sdkPath = findAndroidSdk();
    
    console.log('📋 Checking available Android Virtual Devices...');
    let avds = await listAvds(sdkPath);
    
    if (avds.length === 0) {
      console.log('❌ No AVDs found!');
      console.log('\n📋 To create an AVD:');
      console.log('1. Open Android Studio');
      console.log('2. Go to Tools → AVD Manager');
      console.log('3. Create a new Virtual Device');
      console.log('4. Choose Pixel 4 or similar device');
      console.log('5. Select API 30 or higher');
      console.log('6. Finish setup and try again');
      process.exit(1);
    }

    console.log(`📱 Available AVDs: ${avds.join(', ')}`);
    const selectedAvd = avds[0]; // Use first available AVD

    await startEmulator(sdkPath, selectedAvd);

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure Android Studio is installed');
    console.log('2. Create at least one AVD in Android Studio');
    console.log('3. Check that ANDROID_HOME is set correctly');
    console.log('4. Try running Android Studio first to ensure everything is set up');
    process.exit(1);
  }
}

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n👋 Emulator script terminated.');
  process.exit(0);
});

main();
