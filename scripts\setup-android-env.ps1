# Setup Android Development Environment
Write-Host "🔧 Setting up Android Development Environment..." -ForegroundColor Green

# Function to check if path exists
function Test-PathExists {
    param([string]$Path)
    return Test-Path $Path
}

# Function to add to PATH if not already there
function Add-ToPath {
    param([string]$NewPath)
    if ($env:PATH -notlike "*$NewPath*") {
        $env:PATH += ";$NewPath"
        Write-Host "Added to PATH: $NewPath" -ForegroundColor Green
    } else {
        Write-Host "Already in PATH: $NewPath" -ForegroundColor Yellow
    }
}

# 1. Setup JAVA_HOME
$javaPath = "C:\Program Files\Android\Android Studio\jbr"
if (Test-PathExists $javaPath) {
    $env:JAVA_HOME = $javaPath
    Add-ToPath "$javaPath\bin"
    Write-Host "✅ JAVA_HOME set to: $javaPath" -ForegroundColor Green
} else {
    Write-Host "❌ Java not found at: $javaPath" -ForegroundColor Red
    Write-Host "Please install Android Studio or set JAVA_HOME manually" -ForegroundColor Red
    exit 1
}

# 2. Setup ANDROID_HOME
$androidSdkPath = "C:\Users\<USER>\AppData\Local\Android\Sdk"
if (Test-PathExists $androidSdkPath) {
    $env:ANDROID_HOME = $androidSdkPath
    $env:ANDROID_SDK_ROOT = $androidSdkPath
    Add-ToPath "$androidSdkPath\platform-tools"
    Add-ToPath "$androidSdkPath\tools"
    Add-ToPath "$androidSdkPath\tools\bin"
    Write-Host "✅ ANDROID_HOME set to: $androidSdkPath" -ForegroundColor Green
} else {
    Write-Host "❌ Android SDK not found at: $androidSdkPath" -ForegroundColor Red
    Write-Host "Please install Android Studio and SDK" -ForegroundColor Red
    exit 1
}

# 3. Test Java
Write-Host "🧪 Testing Java..." -ForegroundColor Cyan
try {
    $javaVersion = java -version 2>&1
    Write-Host "✅ Java version: $($javaVersion[0])" -ForegroundColor Green
} catch {
    Write-Host "❌ Java test failed" -ForegroundColor Red
    exit 1
}

# 4. Test ADB
Write-Host "🧪 Testing ADB..." -ForegroundColor Cyan
try {
    $adbVersion = adb version 2>&1
    Write-Host "✅ ADB version: $($adbVersion[0])" -ForegroundColor Green
} catch {
    Write-Host "❌ ADB test failed" -ForegroundColor Red
    exit 1
}

# 5. Check connected devices
Write-Host "📱 Checking connected devices..." -ForegroundColor Cyan
try {
    $devices = adb devices
    Write-Host $devices -ForegroundColor White
    
    $deviceLines = $devices -split "`n" | Where-Object { $_ -match "device$" }
    if ($deviceLines.Count -gt 0) {
        Write-Host "✅ Found $($deviceLines.Count) connected device(s)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  No devices connected. Please start emulator or connect device." -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to check devices" -ForegroundColor Red
}

# 6. Show environment summary
Write-Host "`n📋 Environment Summary:" -ForegroundColor Cyan
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor White
Write-Host "ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor White
Write-Host "PATH includes Android tools: ✅" -ForegroundColor Green

Write-Host "`n🚀 Environment setup complete!" -ForegroundColor Green
Write-Host "You can now run: npm run android:dev" -ForegroundColor Yellow
