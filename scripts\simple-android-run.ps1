# Simple Android App Runner
Write-Host "Setting up Android environment..." -ForegroundColor Green

# Set JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Android\Android Studio\jbr"
$env:PATH += ";$env:JAVA_HOME\bin"

# Set ANDROID_HOME
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = $env:ANDROID_HOME
$env:PATH += ";$env:ANDROID_HOME\platform-tools"
$env:PATH += ";$env:ANDROID_HOME\tools"

Write-Host "Checking Java..." -ForegroundColor Cyan
java -version

Write-Host "Checking ADB..." -ForegroundColor Cyan
adb version

Write-Host "Checking devices..." -ForegroundColor Cyan
adb devices

Write-Host "Building mobile app..." -ForegroundColor Green
npm run build:mobile

if ($LASTEXITCODE -eq 0) {
    Write-Host "Syncing Capacitor..." -ForegroundColor Green
    npx cap sync android
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Running app on Android..." -ForegroundColor Green
        npx cap run android
    }
}
