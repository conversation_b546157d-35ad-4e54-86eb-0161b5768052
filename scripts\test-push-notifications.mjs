#!/usr/bin/env node

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

console.log('🔔 Testing Push Notifications for DANAPEMUDA\n');

// Test notification payloads
const testNotifications = {
  chatMessage: {
    title: "💬 New Chat Message",
    body: "John: Hey, ada rapat besok jam 7 malam ya!",
    data: {
      type: "chat_message",
      chatId: "general",
      senderId: "john123",
      senderName: "<PERSON>",
      messageId: "msg_001",
      messageText: "Hey, ada rapat besok jam 7 malam ya!",
      timestamp: new Date().toISOString()
    },
    android: {
      notification: {
        icon: "ic_chat",
        color: "#5D534B",
        sound: "chat_sound",
        channelId: "chat_messages"
      }
    }
  },

  announcement: {
    title: "📢 Pengumuman Penting",
    body: "Iuran bulan ini diperpanjang sampai tanggal 25",
    data: {
      type: "announcement",
      announcementId: "ann_001",
      title: "Perpanjangan Iuran",
      content: "Iuran bulan ini diperpanjang sampai tanggal 25",
      priority: "high",
      timestamp: new Date().toISOString()
    },
    android: {
      notification: {
        icon: "ic_announcement",
        color: "#FCE09B",
        sound: "announcement_sound",
        channelId: "announcements"
      }
    }
  },

  eventReminder: {
    title: "📅 Event Reminder",
    body: "Gotong royong besok pagi jam 6. Jangan lupa bawa sapu!",
    data: {
      type: "event_reminder",
      eventId: "event_001",
      eventTitle: "Gotong Royong",
      eventDate: "2024-01-15T06:00:00Z",
      reminderType: "day_before"
    },
    android: {
      notification: {
        icon: "ic_event",
        color: "#9DE0D2",
        sound: "reminder_sound",
        channelId: "event_reminders"
      }
    }
  },

  duesReminder: {
    title: "💰 Reminder Iuran",
    body: "Iuran bulan ini belum dibayar. Jatuh tempo: 31 Jan",
    data: {
      type: "dues_reminder",
      memberId: "member_001",
      memberName: "Ahmad",
      amount: 50000,
      dueDate: "2024-01-31",
      monthsOverdue: 0
    },
    android: {
      notification: {
        icon: "ic_money",
        color: "#FF9898",
        sound: "urgent_sound",
        channelId: "dues_reminders"
      }
    }
  }
};

async function sendTestNotification(type, deviceToken) {
  const notification = testNotifications[type];
  
  if (!notification) {
    console.error(`❌ Unknown notification type: ${type}`);
    return;
  }

  console.log(`📤 Sending ${type} notification...`);
  
  // This would normally be sent via your backend server to FCM
  // For testing, we'll simulate the payload structure
  const fcmPayload = {
    to: deviceToken,
    notification: {
      title: notification.title,
      body: notification.body
    },
    data: notification.data,
    android: notification.android
  };

  console.log('📋 FCM Payload:');
  console.log(JSON.stringify(fcmPayload, null, 2));
  console.log('\n✅ Test notification payload generated!\n');
}

async function testAllNotifications(deviceToken) {
  console.log('🧪 Testing all notification types...\n');
  
  for (const [type, notification] of Object.entries(testNotifications)) {
    await sendTestNotification(type, deviceToken);
    
    // Wait 1 second between notifications
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

async function checkADBConnection() {
  try {
    const { stdout } = await execAsync('adb devices');
    const devices = stdout.split('\n').filter(line => 
      line.includes('device') && !line.includes('List of devices')
    );
    
    if (devices.length === 0) {
      console.log('❌ No Android devices connected');
      console.log('📱 Please connect an Android device or start emulator');
      return false;
    }
    
    console.log('✅ Android device connected');
    return true;
  } catch (error) {
    console.log('❌ ADB not found or not working');
    return false;
  }
}

async function installAndTestApp() {
  console.log('📦 Installing and testing app...');
  
  try {
    // Build and install app
    await execAsync('npm run build:mobile');
    console.log('✅ App built successfully');
    
    await execAsync('npx cap sync android');
    console.log('✅ Capacitor synced');
    
    await execAsync('npx cap run android');
    console.log('✅ App installed and launched');
    
    console.log('\n📱 App is now running on your device!');
    console.log('🔔 Check the app for push notification permission prompt');
    
  } catch (error) {
    console.error('❌ Failed to install app:', error.message);
  }
}

async function showTestInstructions() {
  console.log('📋 Push Notification Testing Instructions:\n');
  
  console.log('1. 🔧 SETUP FIREBASE:');
  console.log('   - Create Firebase project at https://console.firebase.google.com');
  console.log('   - Enable Cloud Messaging');
  console.log('   - Download google-services.json');
  console.log('   - Place in android/app/ directory\n');
  
  console.log('2. 📱 TEST ON DEVICE:');
  console.log('   - Connect Android device or start emulator');
  console.log('   - Run: npm run android:dev');
  console.log('   - Grant notification permissions when prompted');
  console.log('   - Check device token in app logs\n');
  
  console.log('3. 🧪 SEND TEST NOTIFICATIONS:');
  console.log('   - Use Firebase Console → Cloud Messaging');
  console.log('   - Or use FCM REST API with device token');
  console.log('   - Test different notification types\n');
  
  console.log('4. ✅ VERIFY FEATURES:');
  console.log('   - Notifications appear in notification tray');
  console.log('   - App opens when notification tapped');
  console.log('   - Correct navigation based on notification type');
  console.log('   - Sound and vibration work');
  console.log('   - Badge count updates\n');
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'install':
      const hasDevice = await checkADBConnection();
      if (hasDevice) {
        await installAndTestApp();
      }
      break;
      
    case 'test':
      const deviceToken = args[1];
      if (!deviceToken) {
        console.log('❌ Please provide device token');
        console.log('Usage: npm run test:push test <device-token>');
        break;
      }
      await testAllNotifications(deviceToken);
      break;
      
    case 'chat':
      const chatToken = args[1] || 'test-token';
      await sendTestNotification('chatMessage', chatToken);
      break;
      
    case 'announcement':
      const annToken = args[1] || 'test-token';
      await sendTestNotification('announcement', annToken);
      break;
      
    case 'event':
      const eventToken = args[1] || 'test-token';
      await sendTestNotification('eventReminder', eventToken);
      break;
      
    case 'dues':
      const duesToken = args[1] || 'test-token';
      await sendTestNotification('duesReminder', duesToken);
      break;
      
    default:
      await showTestInstructions();
      console.log('🚀 Available commands:');
      console.log('   npm run test:push install    - Install app on device');
      console.log('   npm run test:push test <token> - Test all notifications');
      console.log('   npm run test:push chat <token> - Test chat notification');
      console.log('   npm run test:push announcement <token> - Test announcement');
      console.log('   npm run test:push event <token> - Test event reminder');
      console.log('   npm run test:push dues <token> - Test dues reminder');
      break;
  }
}

main().catch(console.error);
