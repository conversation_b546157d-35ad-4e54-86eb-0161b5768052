import { Browser<PERSON>outer } from "react-router-dom";
import { Toaster } from "sonner";
import { AuthProvider } from "@/context/AuthContext";
import { MembersProvider } from "@/context/MembersContext";
import { EventsProvider } from "@/context/EventsContext";
import { ExpensesProvider } from "@/context/ExpensesContext";
import { DuesConfigProvider } from "@/context/DuesConfigContext";
import AnimatedRoutes from "./components/AnimatedRoutes";
import FirebaseErrorBoundary from "./components/FirebaseErrorBoundary";
import MobileAppLayout from "./components/mobile/MobileAppLayout";
import AppErrorBoundary from "./components/AppErrorBoundary";
import PushNotificationProvider from "./components/PushNotificationProvider";

const App = () => {
  console.log('🚀 DANAPEMUDA App starting...');

  return (
  <>
    <Toaster position="top-right" />
    <AppErrorBoundary>
      <FirebaseErrorBoundary>
      <AuthProvider>
        <PushNotificationProvider>
          <MobileAppLayout>
          <MembersProvider>
            <EventsProvider>
              <ExpensesProvider>
                <DuesConfigProvider>
                  <BrowserRouter>
                    <AnimatedRoutes />
                  </BrowserRouter>
                </DuesConfigProvider>
              </ExpensesProvider>
            </EventsProvider>
          </MembersProvider>
        </MobileAppLayout>
        </PushNotificationProvider>
      </AuthProvider>
    </FirebaseErrorBoundary>
    </AppErrorBoundary>
  </>
  );
};

export default App;
