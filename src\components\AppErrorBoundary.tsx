import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class AppErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('App Error Boundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-[#F9F9F9] flex items-center justify-center p-4">
          <div className="bg-white border-4 border-[#5D534B] rounded-lg p-6 max-w-md w-full">
            <h1 className="text-2xl font-black text-[#5D534B] mb-4">
              🚨 App Error
            </h1>
            <p className="text-[#5D534B] mb-4">
              Something went wrong. Please restart the app.
            </p>
            <details className="mb-4">
              <summary className="cursor-pointer text-[#5D534B] font-bold">
                Error Details
              </summary>
              <pre className="text-xs text-red-600 mt-2 overflow-auto">
                {this.state.error?.toString()}
              </pre>
              {this.state.errorInfo && (
                <pre className="text-xs text-red-600 mt-2 overflow-auto">
                  {this.state.errorInfo.componentStack}
                </pre>
              )}
            </details>
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg py-2 px-4 font-bold text-[#5D534B] hover:bg-[#FCE09B] transition-colors"
            >
              Restart App
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default AppErrorBoundary;
