import React from 'react';

const MinimalApp: React.FC = () => {
  console.log('🚀 MinimalApp rendering...');

  return (
    <div className="min-h-screen bg-[#F9F9F9] flex items-center justify-center p-4">
      <div className="bg-white border-4 border-[#5D534B] rounded-lg p-6 max-w-md w-full text-center">
        <h1 className="text-2xl font-black text-[#5D534B] mb-4">
          📱 DANAPEMUDA
        </h1>
        <p className="text-[#5D534B] mb-4">
          Minimal test version
        </p>
        <div className="space-y-2">
          <button 
            onClick={() => console.log('Button clicked!')}
            className="w-full bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg py-2 px-4 font-bold text-[#5D534B]"
          >
            Test Button
          </button>
          <div className="text-sm text-[#5D534B]/70">
            Version: {new Date().toISOString()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MinimalApp;
