import React, { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';

interface PushNotificationProviderProps {
  children: React.ReactNode;
}

const PushNotificationProvider: React.FC<PushNotificationProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializePushNotifications = async () => {
      try {
        console.log('🔔 Initializing push notifications...');

        // Only initialize on native platforms
        if (!Capacitor.isNativePlatform()) {
          console.log('⚠️ Not a native platform, skipping push notifications');
          setIsInitialized(true);
          return;
        }

        // Lazy load push notification service to avoid import errors
        const { pushNotificationService } = await import('../services/PushNotificationService');

        await pushNotificationService.initialize();
        console.log('✅ Push notifications initialized successfully');
        setIsInitialized(true);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize push notifications';
        console.error('❌ Push notification initialization failed:', errorMessage);
        setError(errorMessage);
        setIsInitialized(true); // Continue without push notifications
      }
    };

    if (!isInitialized) {
      initializePushNotifications();
    }
  }, [isInitialized]);

  // Log any initialization errors but don't block the app
  useEffect(() => {
    if (error) {
      console.warn('Push notifications disabled due to error:', error);
    }
  }, [error]);

  return <>{children}</>;
};

export default PushNotificationProvider;
