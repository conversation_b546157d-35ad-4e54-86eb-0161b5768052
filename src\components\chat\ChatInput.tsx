import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Send, Paperclip, Image, Smile, X } from 'lucide-react';
import { useChatContext } from '../../context/ChatContext';
import EmojiPicker from './EmojiPicker';

const ChatInput: React.FC = () => {
  const { sendMessage, setTyping, uploadImage } = useChatContext();
  const [message, setMessage] = useState('');
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, []);

  // Handle typing indicator
  useEffect(() => {
    if (message.trim()) {
      setTyping(true);
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Set new timeout to stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        setTyping(false);
      }, 2000);
    } else {
      setTyping(false);
    }

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [message, setTyping]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedFile) {
      await handleFileUpload();
    } else if (message.trim()) {
      await sendMessage(message.trim());
      setMessage('');
      setTyping(false);
      
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB');
        return;
      }
      
      setSelectedFile(file);
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    try {
      await uploadImage(selectedFile);
      setSelectedFile(null);
      setMessage('');
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Failed to upload file. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const removeSelectedFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const insertEmoji = (emoji: string) => {
    setMessage(prev => prev + emoji);
    setIsEmojiPickerOpen(false);
    textareaRef.current?.focus();
  };

  const isDisabled = isUploading || (!message.trim() && !selectedFile);

  return (
    <div className="border-t-4 border-[#5D534B] bg-white p-4">
      {/* File Preview */}
      {selectedFile && (
        <div className="mb-4 p-3 bg-[#FCE09B] border-2 border-[#5D534B] rounded shadow-[2px_2px_0px_#5D534B]">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {selectedFile.type.startsWith('image/') ? (
                <Image className="w-6 h-6 text-[#5D534B]" />
              ) : (
                <Paperclip className="w-6 h-6 text-[#5D534B]" />
              )}
              <div>
                <p className="font-bold text-[#5D534B] text-sm">{selectedFile.name}</p>
                <p className="text-xs text-[#5D534B]/70">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
            <button
              onClick={removeSelectedFile}
              title="Remove selected file"
              aria-label="Remove selected file"
              className="p-1 hover:bg-[#5D534B]/10 rounded transition-colors"
            >
              <X className="w-4 h-4 text-[#5D534B]" />
            </button>
          </div>
        </div>
      )}

      {/* Input Form */}
      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        {/* File Upload */}
        <div className="relative">
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileSelect}
            accept="image/*,.pdf,.doc,.docx,.txt,.zip,.rar"
            className="hidden"
            aria-label="Select file to upload"
          />
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            title="Attach file"
            aria-label="Attach file"
            className="p-3 bg-[#FCE09B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Paperclip className="w-5 h-5 text-[#5D534B]" />
          </button>
        </div>

        {/* Message Input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => {
              setMessage(e.target.value);
              adjustTextareaHeight();
            }}
            onKeyPress={handleKeyPress}
            placeholder={selectedFile ? "Add a caption..." : "Type a message..."}
            disabled={isUploading}
            className="w-full p-3 pr-12 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded resize-none focus:outline-none focus:shadow-[4px_4px_0px_#5D534B] transition-all disabled:opacity-50 disabled:cursor-not-allowed min-h-[48px] max-h-[120px]"
            rows={1}
          />
          
          {/* Emoji Button */}
          <div className="absolute right-3 bottom-3">
            <button
              type="button"
              onClick={() => setIsEmojiPickerOpen(!isEmojiPickerOpen)}
              disabled={isUploading}
              title="Add emoji"
              aria-label="Add emoji"
              className="p-1 hover:bg-[#FCE09B]/20 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Smile className="w-5 h-5 text-[#5D534B]" />
            </button>
          </div>

          {/* Emoji Picker */}
          {isEmojiPickerOpen && (
            <EmojiPicker
              onEmojiSelect={insertEmoji}
              onClose={() => setIsEmojiPickerOpen(false)}
            />
          )}
        </div>

        {/* Send Button */}
        <button
          type="submit"
          disabled={isDisabled}
          className="p-3 bg-[#FCE09B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] transition-all rounded disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-[2px_2px_0px_#5D534B]"
        >
          {isUploading ? (
            <div className="w-5 h-5 border-2 border-[#5D534B] border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-5 h-5 text-[#5D534B]" />
          )}
        </button>
      </form>

      {/* Character Count */}
      {message.length > 0 && (
        <div className="mt-2 text-right">
          <span className={`text-xs ${
            message.length > 1000 ? 'text-red-500' : 'text-[#5D534B]/70'
          }`}>
            {message.length}/1000
          </span>
        </div>
      )}
    </div>
  );
};

export default ChatInput;
