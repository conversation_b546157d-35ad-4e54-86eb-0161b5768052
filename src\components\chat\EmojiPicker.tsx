import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Smile, Search } from 'lucide-react';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  className?: string;
}

interface EmojiData {
  native: string;
  shortcodes: string;
  unified: string;
}

const EmojiPicker: React.FC<EmojiPickerProps> = ({ onEmojiSelect, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const pickerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Handle emoji selection
  const handleEmojiSelect = (emoji: EmojiData) => {
    onEmojiSelect(emoji.native);
    setIsOpen(false);
  };

  // Quick emoji shortcuts
  const quickEmojis = [
    '😀', '😂', '🥰', '😍', '🤔', '😎', '😭', '😡',
    '👍', '👎', '❤️', '🔥', '💯', '🎉', '👏', '🙏'
  ];

  const handleQuickEmoji = (emoji: string) => {
    onEmojiSelect(emoji);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Quick Emoji Bar - Always Visible */}
      <div className="flex items-center space-x-1 mb-2 overflow-x-auto pb-2">
        {quickEmojis.map((emoji, index) => (
          <button
            key={index}
            type="button"
            onClick={() => handleQuickEmoji(emoji)}
            className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-[#F9F9F9] hover:bg-[#FCE09B] border-2 border-[#5D534B] rounded-lg flex items-center justify-center transition-all duration-200 text-sm sm:text-base"
            title={`Add ${emoji}`}
          >
            {emoji}
          </button>
        ))}

        {/* More Emojis Button */}
        <button
          ref={buttonRef}
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-[#9DE0D2] hover:bg-[#FCE09B] border-2 border-[#5D534B] rounded-lg flex items-center justify-center transition-all duration-200"
          title="More emojis"
        >
          <Smile className="w-4 h-4 sm:w-5 sm:h-5 text-[#5D534B]" />
        </button>
      </div>

      {/* Full Emoji Picker */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={pickerRef}
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-full left-0 mb-2 z-50 bg-white border-4 border-[#5D534B] rounded-lg shadow-[4px_4px_0px_#5D534B] overflow-hidden"
            style={{
              width: 'min(350px, calc(100vw - 32px))',
              maxHeight: '400px'
            }}
          >
            {/* Search Bar */}
            <div className="p-3 border-b-2 border-[#5D534B] bg-[#F9F9F9]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#5D534B]/60" />
                <input
                  type="text"
                  placeholder="Search emojis..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border-2 border-[#5D534B] rounded-lg bg-white text-[#5D534B] placeholder-[#5D534B]/60 focus:outline-none focus:shadow-[2px_2px_0px_#5D534B] transition-all text-sm"
                />
              </div>
            </div>

            {/* Emoji Picker */}
            <div className="emoji-picker-container">
              <Picker
                data={data}
                onEmojiSelect={handleEmojiSelect}
                theme="light"
                set="native"
                searchPosition="none"
                skinTonePosition="none"
                previewPosition="none"
                maxFrequentRows={2}
                perLine={8}
                emojiSize={20}
                emojiButtonSize={28}
                categories={[
                  'frequent',
                  'people',
                  'nature',
                  'foods',
                  'activity',
                  'places',
                  'objects',
                  'symbols',
                  'flags'
                ]}
                style={{
                  width: '100%',
                  border: 'none',
                  fontFamily: 'inherit'
                }}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EmojiPicker;
