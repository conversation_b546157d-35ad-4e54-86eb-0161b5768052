import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, MessageSquare, Send } from 'lucide-react';
import { useSimpleChatContext } from '../../context/SimpleChatContext';
import NameInputModal from './NameInputModal';
import EmojiPicker from './EmojiPicker';
import '../../styles/mobile-chat.css';
import '../../styles/global-responsive.css';

interface SimpleChatContainerProps {
  isOpen: boolean;
  onClose: () => void;
}

const SimpleChatContainer: React.FC<SimpleChatContainerProps> = ({ isOpen, onClose }) => {
  const {
    currentRoom,
    messages,
    isLoading,
    currentUser,
    showNameInput,
    loadingTimeout,
    sendMessage,
    setUserName,
    forceStopLoading
  } = useSimpleChatContext();

  const [messageInput, setMessageInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto scroll ke chat terakhir
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen) {
      // Delay sedikit untuk memastikan modal sudah terbuka
      setTimeout(scrollToBottom, 100);
    }
  }, [isOpen]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!messageInput.trim() || isSending) return;

    setIsSending(true);
    try {
      await sendMessage(messageInput);
      setMessageInput('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };



  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('id-ID', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ x: '100%' }}
        animate={{ x: 0 }}
        exit={{ x: '100%' }}
        transition={{ type: 'tween', duration: 0.3 }}
        className="fixed inset-0 bg-[#F9F9F9] z-50 flex flex-col overflow-hidden"
        style={{
          width: '100vw',
          height: '100dvh',
          maxWidth: '100vw',
          maxHeight: '100dvh',
          minHeight: '100vh',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0
        }}
      >
        {/* Header */}
        <div className="bg-[#FCE09B] border-b-4 border-[#5D534B] flex items-center justify-between flex-shrink-0 mobile-header-safe px-4 py-4">
          <div className="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0">
            <motion.button
              type="button"
              onClick={onClose}
              title="Tutup Chat"
              aria-label="Tutup Chat"
              className="w-8 h-8 sm:w-10 sm:h-10 bg-[#FF9898] border-2 sm:border-3 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] sm:shadow-[3px_3px_0px_#5D534B] hover:shadow-[3px_3px_0px_#5D534B] sm:hover:shadow-[5px_5px_0px_#5D534B] transition-all rounded-full flex items-center justify-center flex-shrink-0"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <X className="w-4 h-4 sm:w-5 sm:h-5 text-[#5D534B] font-bold" />
            </motion.button>
            <MessageSquare className="w-5 h-5 sm:w-6 sm:h-6 text-[#5D534B] flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h2 className="text-sm sm:text-xl font-black text-[#5D534B] truncate">
                {currentRoom?.name || 'DANAPEMUDA Chat'}
              </h2>
              {currentUser && (
                <p className="text-xs sm:text-sm text-[#5D534B]/70 truncate">
                  Sebagai: {currentUser.name}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col overflow-hidden min-h-0">
          {isLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin w-8 h-8 border-4 border-[#5D534B] border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-[#5D534B] font-bold">Memuat chat...</p>
                {loadingTimeout && (
                  <div className="mt-4 p-4 bg-[#FF9898] border-2 border-[#5D534B] rounded">
                    <p className="text-[#5D534B] text-sm mb-2">
                      Loading terlalu lama. Periksa koneksi internet Anda.
                    </p>
                    <div className="flex space-x-2 justify-center">
                      <button
                        type="button"
                        onClick={forceStopLoading}
                        className="bg-white text-[#5D534B] px-4 py-2 border-2 border-[#5D534B] rounded font-bold text-sm"
                      >
                        Lanjutkan
                      </button>
                      <button
                        type="button"
                        onClick={onClose}
                        className="bg-[#FCE09B] text-[#5D534B] px-4 py-2 border-2 border-[#5D534B] rounded font-bold text-sm"
                      >
                        Tutup Chat
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <>
              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto space-y-2 sm:space-y-4 min-h-0 px-4 py-4"
                   style={{
                     background: 'rgba(255, 255, 255, 0.1)',
                     backdropFilter: 'blur(10px)',
                     paddingBottom: 'max(env(safe-area-inset-bottom), 16px)'
                   }}>
                {messages.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="w-16 h-16 text-[#5D534B]/30 mx-auto mb-4" />
                    <p className="text-[#5D534B]/60 font-medium">
                      Belum ada pesan. Mulai percakapan!
                    </p>
                  </div>
                ) : (
                  <>
                    {messages.map((message) => (
                      <motion.div
                        key={message.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={`flex flex-col ${
                          message.senderId === currentUser?.id ? 'items-end' : 'items-start'
                        } mb-4`}
                      >
                      {/* Nama Pengirim */}
                      <p className={`text-xs font-bold text-[#5D534B] mb-1 px-1 ${
                        message.senderId === currentUser?.id ? 'text-right' : 'text-left'
                      }`}>
                        {message.senderId === currentUser?.id ? 'Anda' : message.senderName}
                      </p>

                      {/* Message Bubble */}
                      <div
                        className={`max-w-[80%] p-3 border-2 border-[#5D534B] rounded-lg ${
                          message.senderId === currentUser?.id
                            ? 'bg-[#FCE09B] shadow-[2px_2px_0px_#5D534B]'
                            : 'bg-white shadow-[2px_2px_0px_#5D534B]'
                        }`}
                      >
                        <p className="text-[#5D534B] font-medium break-words">
                          {message.content}
                        </p>
                        <p className="text-xs text-[#5D534B]/60 mt-1">
                          {formatTime(message.timestamp)}
                        </p>
                      </div>
                      </motion.div>
                    ))}
                    {/* Auto scroll target */}
                    <div ref={messagesEndRef} />
                  </>
                )}
              </div>

              {/* Emoji Picker */}
              <div className="px-3 py-2 bg-white border-t-2 border-[#5D534B]/20">
                <EmojiPicker
                  onEmojiSelect={(emoji) => setMessageInput(prev => prev + emoji)}
                  className="w-full"
                />
              </div>

              {/* Input Area */}
              <div className="border-t-4 border-[#5D534B] bg-white flex-shrink-0 mobile-chat-input-area p-4 pb-8" style={{
                paddingBottom: 'max(env(safe-area-inset-bottom), 32px)'
              }}>
                <form onSubmit={handleSendMessage} className="flex items-center space-x-3">
                  <div className="flex-1">
                    <textarea
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      placeholder="Ketik pesan..."
                      className="w-full p-3 border-2 border-[#5D534B] rounded-lg bg-[#F9F9F9] text-[#5D534B] placeholder-[#5D534B]/60 font-medium focus:outline-none focus:shadow-[2px_2px_0px_#5D534B] transition-all resize-none text-sm sm:text-base mobile-chat-textarea min-h-[48px] max-h-[120px]"
                      rows={1}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage(e);
                        }
                      }}
                    />
                  </div>

                  {/* Send Button */}
                  <button
                    type="submit"
                    disabled={!messageInput.trim() || isSending}
                    className="p-3 bg-[#FCE09B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] disabled:opacity-50 disabled:cursor-not-allowed transition-all rounded-lg flex-shrink-0 mobile-chat-button min-h-[48px] min-w-[48px] flex items-center justify-center"
                  >
                    {isSending ? (
                      <div className="animate-spin w-4 h-4 sm:w-5 sm:h-5 border-2 border-[#5D534B] border-t-transparent rounded-full mx-auto"></div>
                    ) : (
                      <Send className="w-4 h-4 sm:w-5 sm:h-5 text-[#5D534B] mx-auto" />
                    )}
                  </button>
                </form>
              </div>
            </>
          )}
        </div>
      </motion.div>

      {/* Name Input Modal */}
      <NameInputModal
        isOpen={showNameInput}
        onSubmit={setUserName}
      />
    </AnimatePresence>
  );
};

export default SimpleChatContainer;
