import React from 'react';
import { motion } from 'framer-motion';

interface AnimatedLogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
  className?: string;
}

const AnimatedLogo: React.FC<AnimatedLogoProps> = ({ 
  size = 'medium', 
  showText = true,
  className = '' 
}) => {
  const sizeClasses = {
    small: 'w-16 h-16',
    medium: 'w-24 h-24',
    large: 'w-32 h-32'
  };

  const textSizes = {
    small: 'text-lg',
    medium: 'text-2xl',
    large: 'text-4xl'
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      {/* Logo Container */}
      <motion.div
        className={`${sizeClasses[size]} relative mb-4`}
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20,
          duration: 1.2
        }}
      >
        {/* Background Circle */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-[#FCE09B] via-[#9DE0D2] to-[#FF9898] rounded-full"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.8 }}
        />
        
        {/* Inner Circle */}
        <motion.div
          className="absolute inset-2 bg-[#5D534B] rounded-full flex items-center justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          {/* Money Symbol */}
          <motion.div
            className="text-[#FCE09B] font-black text-center"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            <div className={`${size === 'large' ? 'text-3xl' : size === 'medium' ? 'text-xl' : 'text-lg'}`}>
              💰
            </div>
          </motion.div>
        </motion.div>

        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-[#FCE09B] rounded-full"
            style={{
              top: `${20 + Math.sin(i * 60 * Math.PI / 180) * 40}%`,
              left: `${50 + Math.cos(i * 60 * Math.PI / 180) * 40}%`,
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: [0, 1, 0.8, 1],
              opacity: [0, 1, 0.7, 1],
              y: [0, -5, 0, -3, 0]
            }}
            transition={{
              delay: 1 + i * 0.1,
              duration: 2,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        ))}
      </motion.div>

      {/* App Name */}
      {showText && (
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.8 }}
        >
          <motion.h1 
            className={`${textSizes[size]} font-black text-[#5D534B] mb-1`}
            initial={{ letterSpacing: '0.5em' }}
            animate={{ letterSpacing: '0.1em' }}
            transition={{ delay: 1.4, duration: 0.6 }}
          >
            DANA<span className="text-[#FF9898]">PEMUDA</span>
          </motion.h1>
          <motion.p 
            className={`${size === 'large' ? 'text-base' : 'text-sm'} text-[#5D534B]/70 font-medium`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.8, duration: 0.5 }}
          >
            Kelola Dana Pemuda Pesayangan
          </motion.p>
        </motion.div>
      )}
    </div>
  );
};

export default AnimatedLogo;
