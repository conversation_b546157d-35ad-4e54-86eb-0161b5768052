import React, { useEffect, useState } from 'react';
import { useMobileApp } from '../../hooks/useMobileApp';
import MobileStatusBar from './MobileStatusBar';
import MobileSafeArea from './MobileSafeArea';
import SplashScreen from './SplashScreen';
import { App } from '@capacitor/app';
import { Keyboard } from '@capacitor/keyboard';
import type { PluginListenerHandle } from '@capacitor/core';

interface MobileAppLayoutProps {
  children: React.ReactNode;
  statusBarStyle?: 'DARK' | 'LIGHT';
  statusBarColor?: string;
  className?: string;
}

const MobileAppLayout: React.FC<MobileAppLayoutProps> = ({
  children,
  statusBarStyle = 'DARK',
  statusBarColor = '#5D534B',
  className = ''
}) => {
  const { isMobileApp } = useMobileApp();
  const [showSplash, setShowSplash] = useState(true);


  useEffect(() => {
    if (isMobileApp) {
      let appStateListener: PluginListenerHandle | null = null;
      let keyboardShowListener: PluginListenerHandle | null = null;
      let keyboardHideListener: PluginListenerHandle | null = null;
      let backButtonListener: PluginListenerHandle | null = null;

      const setupListeners = async () => {
        // Handle app state changes
        const handleAppStateChange = (state: { isActive: boolean }) => {
          if (process.env.NODE_ENV === 'development') {
            console.warn('App state changed:', state);
          }
        };

        // Handle keyboard events
        const handleKeyboardShow = () => {
          document.body.classList.add('keyboard-open');
        };

        const handleKeyboardHide = () => {
          document.body.classList.remove('keyboard-open');
        };

        // Handle back button (Android)
        const handleBackButton = () => {
          // Custom back button logic
          if (process.env.NODE_ENV === 'development') {
            console.warn('Back button pressed');
          }
          return false; // Prevent default behavior
        };

        // Add event listeners
        appStateListener = await App.addListener('appStateChange', handleAppStateChange);
        keyboardShowListener = await Keyboard.addListener('keyboardWillShow', handleKeyboardShow);
        keyboardHideListener = await Keyboard.addListener('keyboardWillHide', handleKeyboardHide);
        backButtonListener = await App.addListener('backButton', handleBackButton);
      };

      setupListeners();

      // Cleanup
      return () => {
        if (appStateListener) appStateListener.remove();
        if (keyboardShowListener) keyboardShowListener.remove();
        if (keyboardHideListener) keyboardHideListener.remove();
        if (backButtonListener) backButtonListener.remove();
      };
    }
  }, [isMobileApp]);

  // If not a mobile app, render children directly
  if (!isMobileApp) {
    return <div className={className}>{children}</div>;
  }

  return (
    <>
      <MobileStatusBar
        style={statusBarStyle}
        backgroundColor={statusBarColor}
      />

      {/* Splash Screen for Mobile App */}
      {showSplash && (
        <SplashScreen
          onComplete={() => setShowSplash(false)}
          duration={3500}
        />
      )}

      <MobileSafeArea className={`min-h-screen-safe ${className}`}>
        {children}
      </MobileSafeArea>
    </>
  );
};

export default MobileAppLayout;
