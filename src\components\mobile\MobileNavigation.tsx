import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, PanInfo } from 'framer-motion';
import { Home, Users, Calendar, Wallet, Settings, LogOut, TrendingDown, MessageSquare, LucideIcon, Vibrate } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useMobileOptimized } from '../../hooks/useMobileOptimized';
import { SimpleChatProvider } from '../../context/SimpleChatContext';
import SimpleChatContainer from '../chat/SimpleChatContainer';
// Haptics will be imported dynamically

interface NavItem {
  icon: LucideIcon;
  label: string;
  href?: string;
  adminOnly?: boolean;
  action?: () => void;
}

const MobileNavigation: React.FC = () => {
  const location = useLocation();
  const { isAuthenticated, logout } = useAuth();
  const { isMobile } = useMobileOptimized();
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [chatProviderMounted, setChatProviderMounted] = useState(false);

  // Haptic feedback helper with dynamic import
  const triggerHaptic = async (style: 'Light' | 'Medium' | 'Heavy' = 'Light') => {
    try {
      const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
      const impactStyle = style === 'Light' ? ImpactStyle.Light :
                         style === 'Medium' ? ImpactStyle.Medium : ImpactStyle.Heavy;
      await Haptics.impact({ style: impactStyle });
    } catch (error) {
      // Haptics not available on web or import failed
      console.log('Haptics not available');
    }
  };

  // Auto-hide navigation on scroll
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false); // Hide when scrolling down
      } else {
        setIsVisible(true); // Show when scrolling up
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  // Don't show on desktop or login page
  if (!isMobile || location.pathname === '/admin/login') {
    return null;
  }

  const handleOpenChat = () => {
    if (!chatProviderMounted) {
      setChatProviderMounted(true);
    }
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
    // Keep provider mounted to avoid re-initialization
  };

  const publicNavItems: NavItem[] = [
    { icon: Wallet, label: 'Keuangan', href: '/' },
    { icon: Users, label: 'Anggota', href: '/members' },
    { icon: Calendar, label: 'Acara', href: '/events' },
    { icon: MessageSquare, label: 'Chat', action: handleOpenChat }
  ];

  const adminNavItems: NavItem[] = [
    { icon: Home, label: 'Dashboard', href: '/admin' },
    { icon: Users, label: 'Anggota', href: '/admin/members' },
    { icon: Calendar, label: 'Acara', href: '/admin/events' },
    { icon: TrendingDown, label: 'Pengeluaran', href: '/admin/expenses' },
    { icon: Settings, label: 'Setting', href: '/admin/dues-settings' }
  ];

  const navItems = isAuthenticated ? adminNavItems : publicNavItems;

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <motion.div
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg"
      style={{
        width: '100vw',
        paddingBottom: 'max(env(safe-area-inset-bottom), 8px)'
      }}
    >
      <div className="flex items-center justify-around px-2 py-2 safe-area-pb">
        {navItems.map((item, index) => {
          const isActive = item.href ? location.pathname === item.href : false;
          const key = item.href || `action-${index}`;

          if (item.action) {
            // Render button for action items (like Chat)
            return (
              <button
                key={key}
                type="button"
                onClick={item.action}
                className="flex flex-col items-center justify-center p-2 min-w-0 flex-1"
              >
                <motion.div
                  whileTap={{ scale: 0.9 }}
                  className="flex flex-col items-center justify-center space-y-1 text-gray-500 hover:text-[#B39DDB]"
                >
                  <div className="p-2 rounded-lg transition-colors hover:bg-gray-100">
                    <item.icon size={20} />
                  </div>
                  <span className="text-xs font-medium truncate max-w-full">
                    {item.label}
                  </span>
                </motion.div>
              </button>
            );
          }

          // Render link for navigation items
          return (
            <Link
              key={key}
              to={item.href!}
              className="flex flex-col items-center justify-center p-2 min-w-0 flex-1"
            >
              <motion.div
                whileTap={{ scale: 0.9 }}
                className={`flex flex-col items-center justify-center space-y-1 ${
                  isActive ? 'text-[#B39DDB]' : 'text-gray-500'
                }`}
              >
                <div className={`p-2 rounded-lg transition-colors ${
                  isActive ? 'bg-[#F3E5F5]' : 'hover:bg-gray-100'
                }`}>
                  <item.icon size={20} />
                </div>
                <span className="text-xs font-medium truncate max-w-full">
                  {item.label}
                </span>
              </motion.div>
            </Link>
          );
        })}
        
        {/* Logout button for admin */}
        {isAuthenticated && (
          <button
            type="button"
            onClick={handleLogout}
            className="flex flex-col items-center justify-center p-2 min-w-0 flex-1"
          >
            <motion.div
              whileTap={{ scale: 0.9 }}
              className="flex flex-col items-center justify-center space-y-1 text-red-500"
            >
              <div className="p-2 rounded-lg hover:bg-red-50 transition-colors">
                <LogOut size={20} />
              </div>
              <span className="text-xs font-medium">
                Keluar
              </span>
            </motion.div>
          </button>
        )}
      </div>

      {/* Chat Modal - Only for public users */}
      {!isAuthenticated && chatProviderMounted && (
        <SimpleChatProvider>
          <SimpleChatContainer
            isOpen={isChatOpen}
            onClose={handleCloseChat}
          />
        </SimpleChatProvider>
      )}
    </motion.div>
  );
};

export default MobileNavigation;
