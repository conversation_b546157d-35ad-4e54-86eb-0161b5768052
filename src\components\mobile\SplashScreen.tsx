import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SplashScreenProps {
  onComplete: () => void;
  duration?: number;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ 
  onComplete, 
  duration = 3000 
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [showProgress, setShowProgress] = useState(false);

  useEffect(() => {
    // Show progress bar after logo animation
    const progressTimer = setTimeout(() => {
      setShowProgress(true);
    }, 2000);

    // Hide splash screen after duration
    const hideTimer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onComplete, 500); // Wait for exit animation
    }, duration);

    return () => {
      clearTimeout(progressTimer);
      clearTimeout(hideTimer);
    };
  }, [onComplete, duration]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 z-50 flex flex-col items-center justify-center"
          style={{
            background: 'linear-gradient(135deg, #F9F9F9 0%, #FCE09B 25%, #9DE0D2 50%, #FF9898 75%, #F9F9F9 100%)',
            backgroundSize: '400% 400%'
          }}
          initial={{ opacity: 0 }}
          animate={{ 
            opacity: 1,
            backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
          }}
          exit={{ 
            opacity: 0,
            scale: 1.1,
            transition: { duration: 0.5 }
          }}
          transition={{
            backgroundPosition: {
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }
          }}
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-4 h-4 bg-[#5D534B] rounded-full"
                style={{
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: [0, 1, 0],
                  opacity: [0, 0.3, 0],
                  y: [0, -20, -40]
                }}
                transition={{
                  delay: Math.random() * 2,
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "loop"
                }}
              />
            ))}
          </div>

          {/* Main Content */}
          <div className="relative z-10 flex flex-col items-center">
            {/* Logo - Splash Screen Only */}
            <motion.div
              className="flex flex-col items-center justify-center mb-4"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{
                type: "spring",
                stiffness: 260,
                damping: 20,
                duration: 1.2
              }}
            >
              {/* Background Circle */}
              <motion.div
                className="w-32 h-32 relative mb-4"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, duration: 0.8 }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-[#FCE09B] via-[#9DE0D2] to-[#FF9898] rounded-full" />

                {/* Inner Circle */}
                <motion.div
                  className="absolute inset-2 bg-[#5D534B] rounded-full flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                >
                  {/* Money Symbol */}
                  <motion.div
                    className="text-[#FCE09B] font-black text-3xl"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                  >
                    💰
                  </motion.div>
                </motion.div>

                {/* Floating Particles */}
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-[#FCE09B] rounded-full"
                    style={{
                      top: `${20 + Math.sin(i * 60 * Math.PI / 180) * 40}%`,
                      left: `${50 + Math.cos(i * 60 * Math.PI / 180) * 40}%`,
                    }}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{
                      scale: [0, 1, 0.8, 1],
                      opacity: [0, 1, 0.7, 1],
                      y: [0, -5, 0, -3, 0]
                    }}
                    transition={{
                      delay: 1 + i * 0.1,
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  />
                ))}
              </motion.div>

              {/* App Name - Original DANAPEMUDA */}
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <motion.h1
                  className="text-4xl font-black text-[#5D534B] mb-1"
                  initial={{ letterSpacing: '0.5em' }}
                  animate={{ letterSpacing: '0.1em' }}
                  transition={{ delay: 1.4, duration: 0.6 }}
                >
                  DANA<span className="text-[#FF9898]">PEMUDA</span>
                </motion.h1>
                <motion.p
                  className="text-base text-[#5D534B]/70 font-medium"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.8, duration: 0.5 }}
                >
                  Kelola Dana Pemuda Pesayangan
                </motion.p>
              </motion.div>
            </motion.div>

            {/* Loading Progress */}
            {showProgress && (
              <motion.div
                className="mt-8 w-64"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {/* Progress Bar Container */}
                <div className="relative">
                  <div className="w-full h-2 bg-[#5D534B]/20 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-[#FCE09B] via-[#9DE0D2] to-[#FF9898] rounded-full"
                      initial={{ width: '0%' }}
                      animate={{ width: '100%' }}
                      transition={{ duration: 1, ease: "easeInOut" }}
                    />
                  </div>
                  
                  {/* Progress Text */}
                  <motion.p
                    className="text-center text-[#5D534B] text-sm font-medium mt-3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    Memuat aplikasi...
                  </motion.p>
                </div>
              </motion.div>
            )}

            {/* Version Info */}
            <motion.div
              className="absolute bottom-8 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2.5 }}
            >
              <p className="text-[#5D534B]/60 text-xs font-medium">
                Version 1.0.0
              </p>
              <p className="text-[#5D534B]/40 text-xs mt-1">
                © 2024 Pemuda Pesayangan
              </p>
            </motion.div>
          </div>

          {/* Floating Elements */}
          <div className="absolute inset-0 pointer-events-none">
            {/* Money Icons */}
            {['💰', '💵', '💳', '📊'].map((icon, i) => (
              <motion.div
                key={i}
                className="absolute text-2xl"
                style={{
                  top: `${20 + i * 20}%`,
                  left: `${10 + i * 20}%`,
                }}
                initial={{ opacity: 0, scale: 0, rotate: -180 }}
                animate={{ 
                  opacity: [0, 0.6, 0],
                  scale: [0, 1, 0],
                  rotate: [0, 180, 360],
                  x: [0, 50, 100],
                  y: [0, -30, -60]
                }}
                transition={{
                  delay: 1 + i * 0.3,
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "loop"
                }}
              >
                {icon}
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SplashScreen;
