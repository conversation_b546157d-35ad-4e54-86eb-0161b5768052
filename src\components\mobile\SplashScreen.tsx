import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SplashScreenProps {
  onComplete: () => void;
  duration?: number;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ 
  onComplete, 
  duration = 3000 
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [showProgress, setShowProgress] = useState(false);

  useEffect(() => {
    // Show progress bar after logo animation
    const progressTimer = setTimeout(() => {
      setShowProgress(true);
    }, 2000);

    // Hide splash screen after duration
    const hideTimer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onComplete, 500); // Wait for exit animation
    }, duration);

    return () => {
      clearTimeout(progressTimer);
      clearTimeout(hideTimer);
    };
  }, [onComplete, duration]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#F9F9F9]"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{
            opacity: 0,
            scale: 1.05,
            transition: { duration: 0.6, ease: "easeInOut" }
          }}
        >
          {/* Subtle Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            {[...Array(8)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-32 h-32 border-2 border-[#5D534B] rounded-full"
                style={{
                  top: `${20 + (i % 3) * 30}%`,
                  left: `${10 + (i % 4) * 25}%`,
                }}
                initial={{ scale: 0, rotate: 0 }}
                animate={{
                  scale: [0, 1, 0.8],
                  rotate: [0, 180, 360],
                  opacity: [0, 0.1, 0]
                }}
                transition={{
                  delay: i * 0.3,
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          {/* Main Content */}
          <div className="relative z-10 flex flex-col items-center">
            {/* Logo DANAPEMUDA - Clean & Professional */}
            <motion.div
              className="flex flex-col items-center justify-center mb-8"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{
                type: "spring",
                stiffness: 100,
                damping: 15,
                duration: 1.0
              }}
            >
              {/* Main Logo Container */}
              <motion.div
                className="relative mb-6"
                initial={{ y: 20 }}
                animate={{ y: 0 }}
                transition={{ delay: 0.3, duration: 0.8, ease: "easeOut" }}
              >
                {/* Logo Background Circle */}
                <motion.div
                  className="w-24 h-24 bg-[#5D534B] rounded-full flex items-center justify-center shadow-lg"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.6, ease: "easeOut" }}
                >
                  {/* DP Letters */}
                  <motion.div
                    className="text-[#FCE09B] font-black text-2xl tracking-tight"
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                  >
                    DP
                  </motion.div>
                </motion.div>

                {/* Subtle Glow Effect */}
                <motion.div
                  className="absolute inset-0 bg-[#FCE09B] rounded-full opacity-20 blur-xl"
                  initial={{ scale: 0 }}
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{
                    delay: 1,
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                  }}
                />
              </motion.div>

              {/* App Name - Clean Typography */}
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.0, duration: 0.7, ease: "easeOut" }}
              >
                <motion.h1
                  className="text-3xl font-black text-[#5D534B] mb-2 tracking-wide"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.2, duration: 0.6 }}
                >
                  DANA<span className="text-[#5D534B]">PEMUDA</span>
                </motion.h1>
                <motion.p
                  className="text-sm text-[#5D534B]/60 font-medium tracking-wide"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.4, duration: 0.5 }}
                >
                  Kelola Dana Pemuda Pesayangan
                </motion.p>
              </motion.div>
            </motion.div>

            {/* Loading Progress - Minimal & Clean */}
            {showProgress && (
              <motion.div
                className="mt-12 w-48"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              >
                {/* Simple Progress Bar */}
                <div className="relative">
                  <div className="w-full h-1 bg-[#5D534B]/10 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-[#5D534B] rounded-full"
                      initial={{ width: '0%' }}
                      animate={{ width: '100%' }}
                      transition={{ duration: 1.2, ease: "easeInOut" }}
                    />
                  </div>

                  {/* Loading Text */}
                  <motion.p
                    className="text-center text-[#5D534B]/50 text-xs font-medium mt-4 tracking-wide"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    Memuat aplikasi...
                  </motion.p>
                </div>
              </motion.div>
            )}

            {/* Version Info - Subtle */}
            <motion.div
              className="absolute bottom-12 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2.0 }}
            >
              <p className="text-[#5D534B]/30 text-xs font-medium">
                Version 1.0.0
              </p>
              <p className="text-[#5D534B]/20 text-xs mt-1">
                © 2024 Pemuda Pesayangan
              </p>
            </motion.div>
          </div>


        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SplashScreen;
