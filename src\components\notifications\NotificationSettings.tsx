import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Bell, 
  BellOff, 
  MessageSquare, 
  Megaphone, 
  Calendar, 
  DollarSign,
  Volume2,
  VolumeX,
  Smartphone
} from 'lucide-react';
import { NotificationSettings as NotificationSettingsType } from '../../types/notifications';
import { usePushNotifications } from '../../hooks/usePushNotifications';

interface NotificationSettingsProps {
  onClose?: () => void;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ onClose }) => {
  const { 
    settings, 
    hasPermission, 
    isLoading, 
    updateSettings, 
    requestPermissions 
  } = usePushNotifications();

  const [localSettings, setLocalSettings] = useState<NotificationSettingsType | null>(settings);

  const handleToggle = (key: keyof NotificationSettingsType) => {
    if (!localSettings) return;

    const newSettings = {
      ...localSettings,
      [key]: !localSettings[key]
    };
    
    setLocalSettings(newSettings);
    updateSettings(newSettings);
  };

  const handleSoundChange = (type: keyof NotificationSettingsType['customSounds'], sound: string) => {
    if (!localSettings) return;

    const newSettings = {
      ...localSettings,
      customSounds: {
        ...localSettings.customSounds,
        [type]: sound
      }
    };
    
    setLocalSettings(newSettings);
    updateSettings(newSettings);
  };

  if (!localSettings) {
    return (
      <div className="responsive-container responsive-page bg-[#F9F9F9]">
        <div className="responsive-content">
          <div className="responsive-loading">
            <div className="animate-spin w-8 h-8 border-4 border-[#5D534B] border-t-transparent rounded-full"></div>
            <p className="mt-4 text-[#5D534B]">Loading notification settings...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="responsive-container responsive-page bg-[#F9F9F9]">
      <div className="responsive-header bg-[#FCE09B] border-b-4 border-[#5D534B]">
        <h1 className="text-responsive-xl font-black text-[#5D534B]">
          🔔 Notification Settings
        </h1>
        {onClose && (
          <button
            onClick={onClose}
            className="touch-button bg-[#FF9898] border-2 border-[#5D534B] rounded-lg"
          >
            ✕
          </button>
        )}
      </div>

      <div className="responsive-content space-responsive-lg">
        {/* Permission Status */}
        <div className="responsive-card">
          <div className="flex items-center space-x-4">
            {hasPermission ? (
              <Bell className="w-8 h-8 text-green-600" />
            ) : (
              <BellOff className="w-8 h-8 text-red-600" />
            )}
            <div className="flex-1">
              <h3 className="text-responsive-lg font-bold text-[#5D534B]">
                Push Notifications
              </h3>
              <p className="text-responsive-sm text-[#5D534B]/70">
                {hasPermission 
                  ? 'Notifications are enabled' 
                  : 'Notifications are disabled'
                }
              </p>
            </div>
            {!hasPermission && (
              <button
                onClick={requestPermissions}
                disabled={isLoading}
                className="touch-button bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg text-responsive-sm font-bold text-[#5D534B]"
              >
                Enable
              </button>
            )}
          </div>
        </div>

        {/* Notification Types */}
        <div className="responsive-card">
          <h3 className="text-responsive-lg font-bold text-[#5D534B] mb-4">
            Notification Types
          </h3>
          
          <div className="space-y-4">
            {/* Chat Messages */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MessageSquare className="w-6 h-6 text-[#5D534B]" />
                <div>
                  <p className="text-responsive-base font-medium text-[#5D534B]">
                    Chat Messages
                  </p>
                  <p className="text-responsive-sm text-[#5D534B]/70">
                    New messages in chat
                  </p>
                </div>
              </div>
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => handleToggle('chatMessages')}
                className={`w-12 h-6 rounded-full border-2 border-[#5D534B] transition-colors ${
                  localSettings.chatMessages ? 'bg-[#9DE0D2]' : 'bg-gray-300'
                }`}
              >
                <motion.div
                  animate={{ x: localSettings.chatMessages ? 24 : 0 }}
                  className="w-4 h-4 bg-[#5D534B] rounded-full m-0.5"
                />
              </motion.button>
            </div>

            {/* Announcements */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Megaphone className="w-6 h-6 text-[#5D534B]" />
                <div>
                  <p className="text-responsive-base font-medium text-[#5D534B]">
                    Announcements
                  </p>
                  <p className="text-responsive-sm text-[#5D534B]/70">
                    Organization announcements
                  </p>
                </div>
              </div>
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => handleToggle('announcements')}
                className={`w-12 h-6 rounded-full border-2 border-[#5D534B] transition-colors ${
                  localSettings.announcements ? 'bg-[#9DE0D2]' : 'bg-gray-300'
                }`}
              >
                <motion.div
                  animate={{ x: localSettings.announcements ? 24 : 0 }}
                  className="w-4 h-4 bg-[#5D534B] rounded-full m-0.5"
                />
              </motion.button>
            </div>

            {/* Event Reminders */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Calendar className="w-6 h-6 text-[#5D534B]" />
                <div>
                  <p className="text-responsive-base font-medium text-[#5D534B]">
                    Event Reminders
                  </p>
                  <p className="text-responsive-sm text-[#5D534B]/70">
                    Upcoming events and activities
                  </p>
                </div>
              </div>
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => handleToggle('eventReminders')}
                className={`w-12 h-6 rounded-full border-2 border-[#5D534B] transition-colors ${
                  localSettings.eventReminders ? 'bg-[#9DE0D2]' : 'bg-gray-300'
                }`}
              >
                <motion.div
                  animate={{ x: localSettings.eventReminders ? 24 : 0 }}
                  className="w-4 h-4 bg-[#5D534B] rounded-full m-0.5"
                />
              </motion.button>
            </div>

            {/* Dues Reminders */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <DollarSign className="w-6 h-6 text-[#5D534B]" />
                <div>
                  <p className="text-responsive-base font-medium text-[#5D534B]">
                    Dues Reminders
                  </p>
                  <p className="text-responsive-sm text-[#5D534B]/70">
                    Payment reminders and overdue notices
                  </p>
                </div>
              </div>
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => handleToggle('duesReminders')}
                className={`w-12 h-6 rounded-full border-2 border-[#5D534B] transition-colors ${
                  localSettings.duesReminders ? 'bg-[#9DE0D2]' : 'bg-gray-300'
                }`}
              >
                <motion.div
                  animate={{ x: localSettings.duesReminders ? 24 : 0 }}
                  className="w-4 h-4 bg-[#5D534B] rounded-full m-0.5"
                />
              </motion.button>
            </div>
          </div>
        </div>

        {/* Sound & Vibration */}
        <div className="responsive-card">
          <h3 className="text-responsive-lg font-bold text-[#5D534B] mb-4">
            Sound & Vibration
          </h3>
          
          <div className="space-y-4">
            {/* Sound */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {localSettings.sound ? (
                  <Volume2 className="w-6 h-6 text-[#5D534B]" />
                ) : (
                  <VolumeX className="w-6 h-6 text-[#5D534B]" />
                )}
                <div>
                  <p className="text-responsive-base font-medium text-[#5D534B]">
                    Sound
                  </p>
                  <p className="text-responsive-sm text-[#5D534B]/70">
                    Play notification sounds
                  </p>
                </div>
              </div>
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => handleToggle('sound')}
                className={`w-12 h-6 rounded-full border-2 border-[#5D534B] transition-colors ${
                  localSettings.sound ? 'bg-[#9DE0D2]' : 'bg-gray-300'
                }`}
              >
                <motion.div
                  animate={{ x: localSettings.sound ? 24 : 0 }}
                  className="w-4 h-4 bg-[#5D534B] rounded-full m-0.5"
                />
              </motion.button>
            </div>

            {/* Vibration */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Smartphone className="w-6 h-6 text-[#5D534B]" />
                <div>
                  <p className="text-responsive-base font-medium text-[#5D534B]">
                    Vibration
                  </p>
                  <p className="text-responsive-sm text-[#5D534B]/70">
                    Vibrate on notifications
                  </p>
                </div>
              </div>
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => handleToggle('vibration')}
                className={`w-12 h-6 rounded-full border-2 border-[#5D534B] transition-colors ${
                  localSettings.vibration ? 'bg-[#9DE0D2]' : 'bg-gray-300'
                }`}
              >
                <motion.div
                  animate={{ x: localSettings.vibration ? 24 : 0 }}
                  className="w-4 h-4 bg-[#5D534B] rounded-full m-0.5"
                />
              </motion.button>
            </div>

            {/* Badge */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Bell className="w-6 h-6 text-[#5D534B]" />
                <div>
                  <p className="text-responsive-base font-medium text-[#5D534B]">
                    Badge Count
                  </p>
                  <p className="text-responsive-sm text-[#5D534B]/70">
                    Show unread count on app icon
                  </p>
                </div>
              </div>
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => handleToggle('badge')}
                className={`w-12 h-6 rounded-full border-2 border-[#5D534B] transition-colors ${
                  localSettings.badge ? 'bg-[#9DE0D2]' : 'bg-gray-300'
                }`}
              >
                <motion.div
                  animate={{ x: localSettings.badge ? 24 : 0 }}
                  className="w-4 h-4 bg-[#5D534B] rounded-full m-0.5"
                />
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
