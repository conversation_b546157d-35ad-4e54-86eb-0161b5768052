import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface TouchButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: LucideIcon;
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

const TouchButton: React.FC<TouchButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  disabled = false,
  className = '',
  type = 'button'
}) => {
  const baseClasses = 'font-bold border-4 border-[#5D534B] transition-all duration-200 flex items-center justify-center gap-2 active:scale-95';
  
  // Minimum 44px touch target for mobile
  const sizeClasses = {
    sm: 'min-h-[44px] px-4 py-2 text-sm',
    md: 'min-h-[48px] px-6 py-3 text-base',
    lg: 'min-h-[56px] px-8 py-4 text-lg'
  };

  const variantClasses = {
    primary: 'bg-[#FCE09B] text-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[6px_6px_0px_#5D534B] active:shadow-[2px_2px_0px_#5D534B]',
    secondary: 'bg-[#9DE0D2] text-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[6px_6px_0px_#5D534B] active:shadow-[2px_2px_0px_#5D534B]',
    danger: 'bg-[#FF9898] text-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[6px_6px_0px_#5D534B] active:shadow-[2px_2px_0px_#5D534B]',
    ghost: 'bg-transparent text-[#5D534B] border-2 hover:bg-[#F9F9F9] active:bg-[#F0F0F0]'
  };

  const disabledClasses = 'opacity-50 cursor-not-allowed pointer-events-none';

  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      className={`
        ${baseClasses}
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${disabled ? disabledClasses : ''}
        ${className}
        rounded-lg
      `}
    >
      {Icon && <Icon className="w-5 h-5" />}
      {children}
    </motion.button>
  );
};

export default TouchButton;
