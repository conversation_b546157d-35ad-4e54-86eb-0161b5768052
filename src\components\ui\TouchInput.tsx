import React, { forwardRef } from 'react';
import { LucideIcon } from 'lucide-react';

interface TouchInputProps {
  label?: string;
  placeholder?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'search';
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  icon?: LucideIcon;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  inputMode?: 'text' | 'numeric' | 'decimal' | 'tel' | 'search' | 'email' | 'url';
}

const TouchInput = forwardRef<HTMLInputElement, TouchInputProps>(({
  label,
  placeholder,
  type = 'text',
  value,
  onChange,
  onFocus,
  onBlur,
  icon: Icon,
  error,
  disabled = false,
  required = false,
  className = '',
  inputMode,
  ...props
}, ref) => {
  return (
    <div className={`w-full ${className}`}>
      {label && (
        <label className="block text-sm font-bold text-[#5D534B] mb-2">
          {label}
          {required && <span className="text-[#FF9898] ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {Icon && (
          <Icon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#5D534B]/60" />
        )}
        
        <input
          ref={ref}
          type={type}
          value={value}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          inputMode={inputMode}
          className={`
            w-full min-h-[48px] px-4 py-3 
            ${Icon ? 'pl-12' : 'pl-4'}
            border-4 border-[#5D534B] rounded-lg 
            bg-white text-[#5D534B] 
            placeholder-[#5D534B]/60 font-medium
            focus:outline-none focus:shadow-[4px_4px_0px_#5D534B] 
            transition-all duration-200
            disabled:opacity-50 disabled:cursor-not-allowed
            ${error ? 'border-[#FF9898] bg-[#FFF5F5]' : ''}
          `}
          {...props}
        />
      </div>
      
      {error && (
        <p className="text-[#FF9898] text-sm mt-1 font-medium">
          {error}
        </p>
      )}
    </div>
  );
});

TouchInput.displayName = 'TouchInput';

export default TouchInput;
