import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { getFirestore } from 'firebase/firestore';

// Firebase configuration for DANAPEMUDA
const firebaseConfig = {
  apiKey: "AIzaSyBR75XOXl28IeElITca355njLgYSTA1AwE",
  authDomain: "pemuda-psy.firebaseapp.com",
  projectId: "pemuda-psy",
  storageBucket: "pemuda-psy.firebasestorage.app",
  messagingSenderId: "792680197911",
  appId: "1:792680197911:android:5f18fba82d8f5321419152"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging
export const messaging = getMessaging(app);

// Initialize Firestore
export const db = getFirestore(app);

// VAPID key for web push (not needed for Android but good to have)
export const vapidKey = "your-vapid-key";

export default app;
