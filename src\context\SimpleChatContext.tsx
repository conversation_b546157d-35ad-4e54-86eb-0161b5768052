import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { UserStorageService, ChatUser } from '../services/userStorageService';
import { ChatRoom, Message } from '../types/chat';
import { firebaseRealtimeChatService, RealtimeChatMessage } from '../services/firebaseRealtimeChat';

interface SimpleChatContextType {
  // State
  currentRoom: ChatRoom | null;
  messages: Message[];
  isLoading: boolean;
  currentUser: ChatUser | null;
  showNameInput: boolean;
  loadingTimeout: boolean;

  // Actions
  sendMessage: (content: string, type?: 'text' | 'image') => Promise<void>;
  uploadImage: (file: File) => Promise<void>;
  setUserName: (name: string) => Promise<void>;
  forceStopLoading: () => void;
}

interface ChatProviderProps {
  children: React.ReactNode;
}

const SimpleChatContext = createContext<SimpleChatContextType | undefined>(undefined);

export const useSimpleChatContext = () => {
  const context = useContext(SimpleChatContext);
  if (!context) {
    throw new Error('useSimpleChatContext must be used within a SimpleChatProvider');
  }
  return context;
};

export const SimpleChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [currentRoom, setCurrentRoom] = useState<ChatRoom | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<ChatUser | null>(null);
  const [showNameInput, setShowNameInput] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const unsubscribeRef = React.useRef<(() => void) | null>(null);

  // Add timeout for loading state
  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        setLoadingTimeout(true);
      }, 8000); // Show timeout after 8 seconds

      return () => clearTimeout(timer);
    } else {
      setLoadingTimeout(false);
    }
  }, [isLoading]);

  // Initialize user and chat system
  useEffect(() => {
    const initializeUser = async () => {
      try {
        setIsLoading(true);
        setLoadingTimeout(false);

        // Check if user exists in local storage
        const savedUser = await UserStorageService.getUser();

        if (savedUser) {
          // User exists, update last active and proceed to chat
          await UserStorageService.updateLastActive(savedUser.id);
          setCurrentUser(savedUser);
          await initializeChat(savedUser);
        } else {
          // New user, show name input
          setShowNameInput(true);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Error initializing user:', error);
        setShowNameInput(true);
        setIsLoading(false);
      }
    };

    initializeUser();

    // Cleanup on unmount
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }

      // Set user offline when component unmounts
      if (currentUser) {
        firebaseRealtimeChatService.setUserPresence(currentUser.id, currentUser.name, false)
          .catch(error => console.warn('Failed to set user offline:', error));
      }
    };
  }, [currentUser]);

  // Initialize chat with user
  const initializeChat = async (chatUser: ChatUser) => {
    try {
      // Create a simple general room
      const generalRoom: ChatRoom = {
        id: 'danapemuda-general-chat',
        name: 'DANAPEMUDA Chat',
        type: 'general',
        participants: [chatUser.id],
        createdBy: chatUser.id,
        isActive: true,
        createdAt: new Date()
      };

      setCurrentRoom(generalRoom);

      // Set user presence online with timeout
      const presencePromise = firebaseRealtimeChatService.setUserPresence(chatUser.id, chatUser.name, true);
      const presenceTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Presence timeout')), 5000)
      );

      try {
        await Promise.race([presencePromise, presenceTimeout]);
      } catch (error) {
        console.warn('Failed to set user presence:', error);
        // Continue anyway, presence is not critical
      }

      // Listen for real-time messages with timeout
      try {
        const unsubscribeMessages = firebaseRealtimeChatService.onMessagesChange((realtimeMessages) => {
          const convertedMessages: Message[] = realtimeMessages.map(msg => ({
            id: msg.id || `msg_${Date.now()}`,
            roomId: generalRoom.id,
            senderId: msg.senderId,
            senderName: msg.senderName,
            content: msg.content,
            type: msg.type,
            timestamp: new Date(msg.timestamp),
            reactions: [],
            isEdited: false
          }));
          setMessages(convertedMessages);
        });

        // Store unsubscribe function for cleanup
        unsubscribeRef.current = unsubscribeMessages;
      } catch (error) {
        console.warn('Failed to setup message listener:', error);
        // Continue anyway, we can still send messages
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing chat:', error);
      setIsLoading(false);
    }
  };



  // Set user name (for new users)
  const setUserName = useCallback(async (name: string) => {
    try {
      const newUser: ChatUser = {
        id: UserStorageService.generateUserId(),
        name: name.trim(),
        avatar: UserStorageService.generateAvatarColor(),
        createdAt: new Date(),
        lastActive: new Date()
      };

      await UserStorageService.saveUser(newUser);
      setCurrentUser(newUser);
      setShowNameInput(false);
      await initializeChat(newUser);
    } catch (error) {
      console.error('Error setting user name:', error);
    }
  }, []);

  // Send message
  const sendMessage = useCallback(async (content: string, type: 'text' | 'image' = 'text') => {
    if (!currentUser || !currentRoom || !content.trim()) return;

    try {
      // Send to Firebase Realtime Database
      await firebaseRealtimeChatService.sendMessage({
        senderId: currentUser.id,
        senderName: currentUser.name,
        content: content.trim(),
        type
      });

      // Messages will be updated via real-time listener
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }, [currentUser, currentRoom]);

  // Upload image
  const uploadImage = useCallback(async (file: File) => {
    if (!file.type.startsWith('image/')) {
      throw new Error('Only image files are allowed');
    }

    try {
      // Convert image to base64 for local storage
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64 = e.target?.result as string;
        await sendMessage(`📷 ${file.name}`, 'image');
        
        // Store image data separately
        const imageData = {
          messageId: `img_${Date.now()}`,
          data: base64,
          fileName: file.name
        };
        
        const savedImages = localStorage.getItem('danapemuda_chat_images') || '[]';
        const images = JSON.parse(savedImages);
        images.push(imageData);
        localStorage.setItem('danapemuda_chat_images', JSON.stringify(images));
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }, [sendMessage]);

  // Force stop loading function
  const forceStopLoading = useCallback(() => {
    setIsLoading(false);
    setLoadingTimeout(false);
  }, []);

  const value: SimpleChatContextType = {
    currentRoom,
    messages,
    isLoading,
    currentUser,
    showNameInput,
    loadingTimeout,
    sendMessage,
    uploadImage,
    setUserName,
    forceStopLoading
  };

  return (
    <SimpleChatContext.Provider value={value}>
      {children}
    </SimpleChatContext.Provider>
  );
};
