import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { UserStorageService, ChatUser } from '../services/userStorageService';
import { ChatRoom, Message } from '../types/chat';
import { firebaseRealtimeChatService, RealtimeChatMessage } from '../services/firebaseRealtimeChat';

interface SimpleChatContextType {
  // State
  currentRoom: ChatRoom | null;
  messages: Message[];
  isLoading: boolean;
  currentUser: ChatUser | null;
  showNameInput: boolean;

  // Actions
  sendMessage: (content: string, type?: 'text' | 'image') => Promise<void>;
  uploadImage: (file: File) => Promise<void>;
  setUserName: (name: string) => Promise<void>;
}

interface ChatProviderProps {
  children: React.ReactNode;
}

const SimpleChatContext = createContext<SimpleChatContextType | undefined>(undefined);

export const useSimpleChatContext = () => {
  const context = useContext(SimpleChatContext);
  if (!context) {
    throw new Error('useSimpleChatContext must be used within a SimpleChatProvider');
  }
  return context;
};

export const SimpleChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [currentRoom, setCurrentRoom] = useState<ChatRoom | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<ChatUser | null>(null);
  const [showNameInput, setShowNameInput] = useState(false);

  // Initialize user and chat system
  useEffect(() => {
    const initializeUser = async () => {
      try {
        setIsLoading(true);
        
        // Check if user exists in local storage
        const savedUser = await UserStorageService.getUser();
        
        if (savedUser) {
          // User exists, update last active and proceed to chat
          await UserStorageService.updateLastActive(savedUser.id);
          setCurrentUser(savedUser);
          await initializeChat(savedUser);
        } else {
          // New user, show name input
          setShowNameInput(true);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Error initializing user:', error);
        setShowNameInput(true);
        setIsLoading(false);
      }
    };

    initializeUser();
  }, []);

  // Initialize chat with user
  const initializeChat = async (chatUser: ChatUser) => {
    try {
      // Create a simple general room
      const generalRoom: ChatRoom = {
        id: 'danapemuda-general-chat',
        name: 'DANAPEMUDA Chat',
        type: 'general',
        participants: [chatUser.id],
        createdBy: chatUser.id,
        isActive: true,
        createdAt: new Date()
      };

      setCurrentRoom(generalRoom);

      // Set user presence online
      await firebaseRealtimeChatService.setUserPresence(chatUser.id, chatUser.name, true);

      // Listen for real-time messages
      const unsubscribeMessages = firebaseRealtimeChatService.onMessagesChange((realtimeMessages) => {
        const convertedMessages: Message[] = realtimeMessages.map(msg => ({
          id: msg.id || `msg_${Date.now()}`,
          roomId: generalRoom.id,
          senderId: msg.senderId,
          senderName: msg.senderName,
          content: msg.content,
          type: msg.type,
          timestamp: new Date(msg.timestamp),
          reactions: [],
          isEdited: false
        }));
        setMessages(convertedMessages);
      });

      // Store unsubscribe function for cleanup
      (window as any).chatUnsubscribe = unsubscribeMessages;

      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing chat:', error);
      setIsLoading(false);
    }
  };

  // Load persisted messages
  const loadPersistedMessages = async () => {
    try {
      const savedMessages = localStorage.getItem('danapemuda_chat_messages');
      if (savedMessages) {
        const parsed = JSON.parse(savedMessages);
        const messages = parsed.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        setMessages(messages);
      }
    } catch (error) {
      console.error('Error loading persisted messages:', error);
    }
  };

  // Save messages to localStorage
  const saveMessagesToStorage = (newMessages: Message[]) => {
    try {
      const messagesToSave = newMessages.map(msg => ({
        ...msg,
        timestamp: msg.timestamp.toISOString()
      }));
      localStorage.setItem('danapemuda_chat_messages', JSON.stringify(messagesToSave));
    } catch (error) {
      console.error('Error saving messages:', error);
    }
  };

  // Set user name (for new users)
  const setUserName = useCallback(async (name: string) => {
    try {
      const newUser: ChatUser = {
        id: UserStorageService.generateUserId(),
        name: name.trim(),
        avatar: UserStorageService.generateAvatarColor(),
        createdAt: new Date(),
        lastActive: new Date()
      };

      await UserStorageService.saveUser(newUser);
      setCurrentUser(newUser);
      setShowNameInput(false);
      await initializeChat(newUser);
    } catch (error) {
      console.error('Error setting user name:', error);
    }
  }, []);

  // Send message
  const sendMessage = useCallback(async (content: string, type: 'text' | 'image' = 'text') => {
    if (!currentUser || !currentRoom || !content.trim()) return;

    try {
      // Send to Firebase Realtime Database
      await firebaseRealtimeChatService.sendMessage({
        senderId: currentUser.id,
        senderName: currentUser.name,
        content: content.trim(),
        type
      });

      // Messages will be updated via real-time listener
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }, [currentUser, currentRoom]);

  // Upload image
  const uploadImage = useCallback(async (file: File) => {
    if (!file.type.startsWith('image/')) {
      throw new Error('Only image files are allowed');
    }

    try {
      // Convert image to base64 for local storage
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64 = e.target?.result as string;
        await sendMessage(`📷 ${file.name}`, 'image');
        
        // Store image data separately
        const imageData = {
          messageId: `img_${Date.now()}`,
          data: base64,
          fileName: file.name
        };
        
        const savedImages = localStorage.getItem('danapemuda_chat_images') || '[]';
        const images = JSON.parse(savedImages);
        images.push(imageData);
        localStorage.setItem('danapemuda_chat_images', JSON.stringify(images));
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }, [sendMessage]);

  const value: SimpleChatContextType = {
    currentRoom,
    messages,
    isLoading,
    currentUser,
    showNameInput,
    sendMessage,
    uploadImage,
    setUserName
  };

  return (
    <SimpleChatContext.Provider value={value}>
      {children}
    </SimpleChatContext.Provider>
  );
};
