import { useState, useCallback } from 'react';
import { Camera, CameraResultType, CameraSource, Photo } from '@capacitor/camera';
import { Capacitor } from '@capacitor/core';

interface CameraOptions {
  quality?: number;
  allowEditing?: boolean;
  resultType?: CameraResultType;
  source?: CameraSource;
  width?: number;
  height?: number;
}

interface CameraState {
  isLoading: boolean;
  error: string | null;
  hasPermission: boolean;
}

export const useCamera = () => {
  const [state, setState] = useState<CameraState>({
    isLoading: false,
    error: null,
    hasPermission: false
  });

  // Check camera permissions
  const checkPermissions = useCallback(async () => {
    try {
      const permissions = await Camera.checkPermissions();
      const hasPermission = permissions.camera === 'granted';
      
      setState(prev => ({ ...prev, hasPermission }));
      return hasPermission;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to check camera permissions',
        hasPermission: false 
      }));
      return false;
    }
  }, []);

  // Request camera permissions
  const requestPermissions = useCallback(async () => {
    try {
      const permissions = await Camera.requestPermissions();
      const hasPermission = permissions.camera === 'granted';
      
      setState(prev => ({ ...prev, hasPermission, error: null }));
      return hasPermission;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Camera permission denied',
        hasPermission: false 
      }));
      return false;
    }
  }, []);

  // Take photo from camera
  const takePhoto = useCallback(async (options: CameraOptions = {}) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Check permissions first
      const hasPermission = await checkPermissions() || await requestPermissions();
      
      if (!hasPermission) {
        throw new Error('Camera permission required');
      }

      const photo = await Camera.getPhoto({
        quality: options.quality || 90,
        allowEditing: options.allowEditing || false,
        resultType: options.resultType || CameraResultType.DataUrl,
        source: options.source || CameraSource.Camera,
        width: options.width,
        height: options.height
      });

      setState(prev => ({ ...prev, isLoading: false }));
      return photo;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to take photo';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      throw error;
    }
  }, [checkPermissions, requestPermissions]);

  // Pick photo from gallery
  const pickPhoto = useCallback(async (options: CameraOptions = {}) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const photo = await Camera.getPhoto({
        quality: options.quality || 90,
        allowEditing: options.allowEditing || false,
        resultType: options.resultType || CameraResultType.DataUrl,
        source: CameraSource.Photos,
        width: options.width,
        height: options.height
      });

      setState(prev => ({ ...prev, isLoading: false }));
      return photo;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to pick photo';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      throw error;
    }
  }, []);

  // Show action sheet for photo source selection
  const showPhotoOptions = useCallback(async (options: CameraOptions = {}) => {
    if (!Capacitor.isNativePlatform()) {
      // On web, directly open file picker
      return pickPhoto(options);
    }

    return new Promise<Photo>((resolve, reject) => {
      const actionSheet = document.createElement('div');
      actionSheet.innerHTML = `
        <div style="
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          background: white;
          border-top: 4px solid #5D534B;
          z-index: 9999;
          padding: 20px;
          transform: translateY(100%);
          transition: transform 0.3s ease;
        ">
          <h3 style="margin: 0 0 20px 0; font-weight: bold; color: #5D534B;">Pilih Sumber Foto</h3>
          <button id="camera-btn" style="
            width: 100%;
            padding: 15px;
            margin-bottom: 10px;
            background: #FCE09B;
            border: 3px solid #5D534B;
            border-radius: 8px;
            font-weight: bold;
            color: #5D534B;
          ">📷 Ambil Foto</button>
          <button id="gallery-btn" style="
            width: 100%;
            padding: 15px;
            margin-bottom: 10px;
            background: #9DE0D2;
            border: 3px solid #5D534B;
            border-radius: 8px;
            font-weight: bold;
            color: #5D534B;
          ">🖼️ Pilih dari Galeri</button>
          <button id="cancel-btn" style="
            width: 100%;
            padding: 15px;
            background: #FF9898;
            border: 3px solid #5D534B;
            border-radius: 8px;
            font-weight: bold;
            color: #5D534B;
          ">❌ Batal</button>
        </div>
      `;

      document.body.appendChild(actionSheet);
      
      // Animate in
      setTimeout(() => {
        const sheet = actionSheet.firstElementChild as HTMLElement;
        sheet.style.transform = 'translateY(0)';
      }, 10);

      const cleanup = () => {
        const sheet = actionSheet.firstElementChild as HTMLElement;
        sheet.style.transform = 'translateY(100%)';
        setTimeout(() => {
          document.body.removeChild(actionSheet);
        }, 300);
      };

      actionSheet.querySelector('#camera-btn')?.addEventListener('click', async () => {
        cleanup();
        try {
          const photo = await takePhoto(options);
          resolve(photo);
        } catch (error) {
          reject(error);
        }
      });

      actionSheet.querySelector('#gallery-btn')?.addEventListener('click', async () => {
        cleanup();
        try {
          const photo = await pickPhoto(options);
          resolve(photo);
        } catch (error) {
          reject(error);
        }
      });

      actionSheet.querySelector('#cancel-btn')?.addEventListener('click', () => {
        cleanup();
        reject(new Error('User cancelled'));
      });
    });
  }, [takePhoto, pickPhoto]);

  return {
    ...state,
    takePhoto,
    pickPhoto,
    showPhotoOptions,
    checkPermissions,
    requestPermissions
  };
};
