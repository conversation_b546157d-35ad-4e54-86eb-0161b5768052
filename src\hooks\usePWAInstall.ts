import { useState, useEffect, useCallback } from 'react';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface PWAInstallState {
  isInstallable: boolean;
  isInstalled: boolean;
  isStandalone: boolean;
  platform: 'ios' | 'android' | 'desktop' | 'unknown';
}

export const usePWAInstall = () => {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [state, setState] = useState<PWAInstallState>({
    isInstallable: false,
    isInstalled: false,
    isStandalone: false,
    platform: 'unknown'
  });

  // Detect platform
  const detectPlatform = useCallback((): PWAInstallState['platform'] => {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/iphone|ipad|ipod/.test(userAgent)) {
      return 'ios';
    } else if (/android/.test(userAgent)) {
      return 'android';
    } else if (/windows|mac|linux/.test(userAgent)) {
      return 'desktop';
    }
    
    return 'unknown';
  }, []);

  // Check if app is running in standalone mode
  const checkStandaloneMode = useCallback(() => {
    return (
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as any).standalone === true ||
      document.referrer.includes('android-app://')
    );
  }, []);

  // Check if app is already installed
  const checkInstallStatus = useCallback(() => {
    const isStandalone = checkStandaloneMode();
    const isInstalled = isStandalone || localStorage.getItem('pwa-installed') === 'true';
    
    return { isStandalone, isInstalled };
  }, [checkStandaloneMode]);

  useEffect(() => {
    const platform = detectPlatform();
    const { isStandalone, isInstalled } = checkInstallStatus();
    
    setState(prev => ({
      ...prev,
      platform,
      isStandalone,
      isInstalled
    }));

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const promptEvent = e as BeforeInstallPromptEvent;
      setInstallPrompt(promptEvent);
      
      setState(prev => ({
        ...prev,
        isInstallable: true
      }));
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setInstallPrompt(null);
      localStorage.setItem('pwa-installed', 'true');
      
      setState(prev => ({
        ...prev,
        isInstallable: false,
        isInstalled: true
      }));
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [detectPlatform, checkInstallStatus]);

  // Install PWA
  const installPWA = useCallback(async () => {
    if (!installPrompt) {
      return { success: false, error: 'Install prompt not available' };
    }

    try {
      await installPrompt.prompt();
      const choiceResult = await installPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        setInstallPrompt(null);
        setState(prev => ({
          ...prev,
          isInstallable: false,
          isInstalled: true
        }));
        
        return { success: true, error: null };
      } else {
        return { success: false, error: 'User dismissed install prompt' };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Install failed' 
      };
    }
  }, [installPrompt]);

  // Get install instructions for different platforms
  const getInstallInstructions = useCallback(() => {
    switch (state.platform) {
      case 'ios':
        return {
          title: 'Install DANAPEMUDA',
          steps: [
            'Tap the Share button in Safari',
            'Scroll down and tap "Add to Home Screen"',
            'Tap "Add" to install the app'
          ],
          icon: '📱'
        };
      
      case 'android':
        return {
          title: 'Install DANAPEMUDA',
          steps: [
            'Tap the menu button (⋮) in Chrome',
            'Select "Add to Home screen"',
            'Tap "Add" to install the app'
          ],
          icon: '🤖'
        };
      
      case 'desktop':
        return {
          title: 'Install DANAPEMUDA',
          steps: [
            'Click the install button in the address bar',
            'Or use the menu: More tools → Create shortcut',
            'Check "Open as window" for app experience'
          ],
          icon: '💻'
        };
      
      default:
        return {
          title: 'Install DANAPEMUDA',
          steps: [
            'Look for install option in your browser',
            'Add to home screen or desktop',
            'Enjoy the app experience!'
          ],
          icon: '📲'
        };
    }
  }, [state.platform]);

  // Show install banner
  const showInstallBanner = useCallback(() => {
    if (state.isInstalled || !state.isInstallable) {
      return false;
    }

    // Don't show banner if user dismissed it recently
    const lastDismissed = localStorage.getItem('install-banner-dismissed');
    if (lastDismissed) {
      const dismissedTime = new Date(lastDismissed);
      const now = new Date();
      const daysSinceDismissed = (now.getTime() - dismissedTime.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceDismissed < 7) {
        return false;
      }
    }

    return true;
  }, [state.isInstalled, state.isInstallable]);

  // Dismiss install banner
  const dismissInstallBanner = useCallback(() => {
    localStorage.setItem('install-banner-dismissed', new Date().toISOString());
  }, []);

  return {
    ...state,
    installPWA,
    getInstallInstructions,
    showInstallBanner: showInstallBanner(),
    dismissInstallBanner
  };
};
