import { useEffect, useState, useCallback } from 'react';

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  loadTime: number;
  renderTime: number;
  isSlowDevice: boolean;
}

export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    memoryUsage: 0,
    loadTime: 0,
    renderTime: 0,
    isSlowDevice: false
  });

  // FPS monitoring
  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        setMetrics(prev => ({
          ...prev,
          fps,
          isSlowDevice: fps < 30
        }));
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };

    animationId = requestAnimationFrame(measureFPS);

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, []);

  // Memory usage monitoring
  useEffect(() => {
    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = Math.round(
          (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
        );
        
        setMetrics(prev => ({ ...prev, memoryUsage }));
      }
    };

    measureMemory();
    const interval = setInterval(measureMemory, 5000);

    return () => clearInterval(interval);
  }, []);

  // Load time measurement
  useEffect(() => {
    const measureLoadTime = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation.loadEventEnd - navigation.navigationStart;
      
      setMetrics(prev => ({ ...prev, loadTime }));
    };

    if (document.readyState === 'complete') {
      measureLoadTime();
    } else {
      window.addEventListener('load', measureLoadTime);
      return () => window.removeEventListener('load', measureLoadTime);
    }
  }, []);

  // Render time measurement
  const measureRenderTime = useCallback((componentName: string) => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
      
      setMetrics(prev => ({ ...prev, renderTime }));
    };
  }, []);

  // Device capability detection
  const getDeviceCapability = useCallback(() => {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    const deviceCapability = {
      cores: navigator.hardwareConcurrency || 1,
      memory: (navigator as any).deviceMemory || 'unknown',
      webgl: !!gl,
      webgl2: !!canvas.getContext('webgl2'),
      touchSupport: 'ontouchstart' in window,
      connectionType: (navigator as any).connection?.effectiveType || 'unknown'
    };

    return deviceCapability;
  }, []);

  // Performance optimization suggestions
  const getOptimizationSuggestions = useCallback(() => {
    const suggestions: string[] = [];
    
    if (metrics.fps < 30) {
      suggestions.push('Consider reducing animations or visual effects');
    }
    
    if (metrics.memoryUsage > 80) {
      suggestions.push('Memory usage is high, consider lazy loading or data cleanup');
    }
    
    if (metrics.loadTime > 3000) {
      suggestions.push('Page load time is slow, consider code splitting or asset optimization');
    }
    
    if (metrics.renderTime > 16) {
      suggestions.push('Component render time is slow, consider memoization or virtualization');
    }

    return suggestions;
  }, [metrics]);

  return {
    metrics,
    measureRenderTime,
    getDeviceCapability,
    getOptimizationSuggestions
  };
};

// Performance wrapper component
interface PerformanceWrapperProps {
  name: string;
  children: React.ReactNode;
  onSlowRender?: (time: number) => void;
}

export const PerformanceWrapper: React.FC<PerformanceWrapperProps> = ({
  name,
  children,
  onSlowRender
}) => {
  const { measureRenderTime } = usePerformanceMonitor();

  useEffect(() => {
    const endMeasure = measureRenderTime(name);
    
    return () => {
      const renderTime = endMeasure();
      if (renderTime > 16 && onSlowRender) {
        onSlowRender(renderTime);
      }
    };
  });

  return <>{children}</>;
};
