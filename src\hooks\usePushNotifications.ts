import { useState, useEffect, useCallback } from 'react';
import { pushNotificationService } from '../services/PushNotificationService';
import { 
  NotificationSettings, 
  NotificationHistory, 
  NotificationPermissionStatus 
} from '../types/notifications';

interface UsePushNotificationsReturn {
  isInitialized: boolean;
  hasPermission: boolean;
  token: string | null;
  settings: NotificationSettings | null;
  history: NotificationHistory[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  initialize: () => Promise<void>;
  requestPermissions: () => Promise<void>;
  updateSettings: (settings: NotificationSettings) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  clearHistory: () => Promise<void>;
  refreshHistory: () => Promise<void>;
}

export const usePushNotifications = (): UsePushNotificationsReturn => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [history, setHistory] = useState<NotificationHistory[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize push notifications
  const initialize = useCallback(async () => {
    if (isInitialized) return;

    setIsLoading(true);
    setError(null);

    try {
      await pushNotificationService.initialize();
      
      // Get permission status
      const permissions = await pushNotificationService.checkPermissions();
      setHasPermission(permissions.receive === 'granted');
      
      // Get token
      const currentToken = await pushNotificationService.getToken();
      setToken(currentToken);
      
      // Load settings
      const currentSettings = await pushNotificationService.getNotificationSettings();
      setSettings(currentSettings);
      
      // Load history
      const currentHistory = await pushNotificationService.getNotificationHistory();
      setHistory(currentHistory);
      
      // Get unread count
      const currentUnreadCount = await pushNotificationService.getUnreadCount();
      setUnreadCount(currentUnreadCount);
      
      setIsInitialized(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize push notifications';
      setError(errorMessage);
      console.error('❌ Push notification initialization error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized]);

  // Request permissions
  const requestPermissions = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const permissions = await pushNotificationService.requestPermissions();
      setHasPermission(permissions.receive === 'granted');
      
      if (permissions.receive === 'granted') {
        // Re-initialize to get token
        await initialize();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to request permissions';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [initialize]);

  // Update notification settings
  const updateSettings = useCallback(async (newSettings: NotificationSettings) => {
    setIsLoading(true);
    setError(null);

    try {
      await pushNotificationService.saveNotificationSettings(newSettings);
      setSettings(newSettings);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      await pushNotificationService.markAllAsRead();
      
      // Refresh history and unread count
      const updatedHistory = await pushNotificationService.getNotificationHistory();
      setHistory(updatedHistory);
      setUnreadCount(0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark as read';
      setError(errorMessage);
    }
  }, []);

  // Clear notification history
  const clearHistory = useCallback(async () => {
    try {
      await pushNotificationService.clearNotificationHistory();
      setHistory([]);
      setUnreadCount(0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear history';
      setError(errorMessage);
    }
  }, []);

  // Refresh notification history
  const refreshHistory = useCallback(async () => {
    try {
      const currentHistory = await pushNotificationService.getNotificationHistory();
      setHistory(currentHistory);
      
      const currentUnreadCount = await pushNotificationService.getUnreadCount();
      setUnreadCount(currentUnreadCount);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh history';
      setError(errorMessage);
    }
  }, []);

  // Listen for in-app notifications
  useEffect(() => {
    const handleInAppNotification = (event: CustomEvent) => {
      // Handle in-app notification display
      console.log('📨 In-app notification:', event.detail);
      
      // Refresh history when new notification arrives
      refreshHistory();
    };

    const handleNavigateToChat = (event: CustomEvent) => {
      // Handle navigation to chat
      console.log('💬 Navigate to chat:', event.detail);
    };

    const handleNavigateToAnnouncements = () => {
      // Handle navigation to announcements
      console.log('📢 Navigate to announcements');
    };

    const handleNavigateToEvents = (event: CustomEvent) => {
      // Handle navigation to events
      console.log('📅 Navigate to events:', event.detail);
    };

    const handleNavigateToFinance = () => {
      // Handle navigation to finance
      console.log('💰 Navigate to finance');
    };

    // Add event listeners
    window.addEventListener('show-in-app-notification', handleInAppNotification as EventListener);
    window.addEventListener('navigate-to-chat', handleNavigateToChat as EventListener);
    window.addEventListener('navigate-to-announcements', handleNavigateToAnnouncements);
    window.addEventListener('navigate-to-events', handleNavigateToEvents as EventListener);
    window.addEventListener('navigate-to-finance', handleNavigateToFinance);

    return () => {
      // Cleanup event listeners
      window.removeEventListener('show-in-app-notification', handleInAppNotification as EventListener);
      window.removeEventListener('navigate-to-chat', handleNavigateToChat as EventListener);
      window.removeEventListener('navigate-to-announcements', handleNavigateToAnnouncements);
      window.removeEventListener('navigate-to-events', handleNavigateToEvents as EventListener);
      window.removeEventListener('navigate-to-finance', handleNavigateToFinance);
    };
  }, [refreshHistory]);

  // Auto-initialize on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  return {
    isInitialized,
    hasPermission,
    token,
    settings,
    history,
    unreadCount,
    isLoading,
    error,
    
    // Actions
    initialize,
    requestPermissions,
    updateSettings,
    markAllAsRead,
    clearHistory,
    refreshHistory
  };
};
