@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fullscreen Mobile App Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  overflow: hidden;
  position: fixed;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #F9F9F9;
}

#root {
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* Prevent zoom on input focus (iOS) - Better than viewport meta */
input, textarea, select {
  font-size: 16px !important;
  -webkit-appearance: none;
  border-radius: 0;
  transform-origin: left top;
  transform: scale(1);
}

/* Prevent pinch zoom while allowing accessibility zoom */
html {
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}

/* Prevent double-tap zoom */
* {
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}

/* Disable pull-to-refresh - Cross-browser approach */
body {
  /* Modern browsers */
  overscroll-behavior-y: none;
  overscroll-behavior: none;
  /* Fallback for older browsers */
  touch-action: pan-x pan-y;
  /* Mobile scroll optimization */
  -webkit-overflow-scrolling: touch;
}

/* Feature detection for overscroll-behavior */
@supports (overscroll-behavior: none) {
  body {
    overscroll-behavior: none;
  }
}

/* Fallback for browsers without overscroll-behavior support */
@supports not (overscroll-behavior: none) {
  body {
    position: fixed;
    overflow: hidden;
  }

  #root {
    overflow-y: auto;
    height: 100vh;
    height: 100dvh;
  }
}

/* Disable text selection on UI elements */
button, .touch-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* Smooth scrolling for content areas */
.scrollable {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Mobile-first responsive utilities */
@layer utilities {
  /* Safe area utilities for mobile devices */
  .safe-area-pt {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-pl {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-pr {
    padding-right: env(safe-area-inset-right);
  }

  /* Touch-friendly tap targets */
  .tap-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Optimized scrolling for mobile */
  .scroll-smooth-mobile {
    scroll-behavior: smooth;
    /* Hide scrollbars cross-browser */
    -ms-overflow-style: none;
  }

  /* Modern browsers */
  @supports (scrollbar-width: none) {
    .scroll-smooth-mobile {
      scrollbar-width: none;
    }
  }

  /* Webkit browsers */
  .scroll-smooth-mobile::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  /* Prevent zoom on input focus (iOS) */
  .no-zoom {
    font-size: 16px;
  }

  @media (max-width: 640px) {
    .no-zoom {
      font-size: 16px !important;
    }
  }

  /* Mobile-optimized animations */
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Improved focus states for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:ring-offset-2;
  }

  /* Mobile-optimized card shadows */
  .card-shadow-mobile {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  @media (min-width: 768px) {
    .card-shadow-mobile {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
  }
}

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 33 11% 33%;

    --card: 0 0% 100%;
    --card-foreground: 33 11% 33%;

    --popover: 0 0% 100%;
    --popover-foreground: 33 11% 33%;

    --primary: 0 100% 85%;
    --primary-foreground: 33 11% 33%;

    --secondary: 160 25% 77%;
    --secondary-foreground: 33 11% 33%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 33 11% 33%;

    --accent: 33 11% 33%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 33 11% 33%;
    --input: 0 0% 89.8%;
    --ring: 33 11% 33%;

    --radius: 1.5rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 33 11% 33%;
    --sidebar-primary: 345 100% 66%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 96.1%;
    --sidebar-accent-foreground: 33 11% 33%;
    --sidebar-border: 33 11% 33%;
    --sidebar-ring: 33 11% 33%;
  }
}

@layer base {
  html, body {
    @apply text-base sm:text-sm md:text-base lg:text-lg bg-[#F9F9F9];
    scroll-behavior: smooth;
    font-family: 'Nunito', sans-serif;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-[#F9F9F9] text-[#5D534B] font-bold;
  }

  * {
    @apply box-border;
  }
}

@layer components {
  .neo-card {
    @apply bg-white border-4 border-[#5D534B] shadow-pastel transition-all duration-200 rounded-2xl;
  }
  
  .neo-button {
    @apply bg-[#FF9898] text-[#5D534B] border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm transition-all duration-200 rounded-full;
  }
  
  .neo-button-blue {
    @apply bg-[#9DE0D2] text-[#5D534B] border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm transition-all duration-200 rounded-full;
  }
  
  .neo-button-yellow {
    @apply bg-[#FCE09B] text-[#5D534B] border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm transition-all duration-200 rounded-full;
  }
  
  .neo-button-green {
    @apply bg-[#9DE0D2] text-[#5D534B] border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm transition-all duration-200 rounded-full;
  }
  
  .neo-input {
    @apply bg-white border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm focus:outline-none rounded-2xl;
  }
  
  .neo-table {
    @apply w-full border-4 border-[#5D534B] rounded-2xl;
  }
  
  .neo-table th {
    @apply bg-[#9DE0D2] text-[#5D534B] border-4 border-[#5D534B] p-2 text-left;
  }
  
  .neo-table td {
    @apply border-4 border-[#5D534B] p-2;
  }
  
  .neo-table tr:nth-child(even) {
    @apply bg-[#FCE09B] bg-opacity-20;
  }
  
  .neo-gradient-pink {
    @apply bg-gradient-to-br from-white to-[#FF9898]/50;
  }
  
  .neo-gradient-blue {
    @apply bg-gradient-to-br from-white to-[#9DE0D2]/50;
  }
  
  .neo-gradient-yellow {
    @apply bg-gradient-to-br from-white to-[#FCE09B]/50;
  }
  
  .neo-gradient-green {
    @apply bg-gradient-to-br from-white to-[#9DE0D2]/50;
  }

  .expense-card {
    @apply neo-card p-4 mb-3 border-4 border-[#5D534B] neo-gradient-pink;
  }
  
  .income-card {
    @apply neo-card p-4 mb-3 border-4 border-[#5D534B] neo-gradient-green;
  }
  
  .border-neo-pink {
    @apply border-4 border-[#FF9898];
  }
  
  .border-neo-blue {
    @apply border-4 border-[#9DE0D2];
  }
  
  .border-neo-yellow {
    @apply border-4 border-[#FCE09B];
  }
  
  .border-neo-green {
    @apply border-4 border-[#9DE0D2];
  }
}

@layer utilities {
  .responsive-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6;
  }
}

/* Animation Support */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

/* Page Transitions */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* Improved animations for neobrutalism style - GPU optimized */
@keyframes float {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, -5px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
  will-change: transform;
  /* Performance optimizations */
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Disable animations on low-end devices */
@media (prefers-reduced-motion: reduce) {
  .animate-float {
    animation: none;
    will-change: auto;
  }
}

/* Performance optimization for mobile */
@media (max-width: 768px) {
  .animate-float {
    animation-duration: 4s; /* Slower animation for mobile */
  }
}

/* Add some prefers-reduced-motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

