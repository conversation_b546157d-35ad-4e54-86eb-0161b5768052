import { 
  PushNotifications, 
  PushNotificationSchema, 
  ActionPerformed,
  Token,
  PermissionStatus
} from '@capacitor/push-notifications';
import { Capacitor } from '@capacitor/core';
import { Preferences } from '@capacitor/preferences';
import { 
  NotificationData, 
  NotificationSettings, 
  PushNotificationToken,
  NotificationHistory 
} from '../types/notifications';

class PushNotificationService {
  private isInitialized = false;
  private currentToken: string | null = null;
  private notificationHistory: NotificationHistory[] = [];

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('ℹ️ Push notifications already initialized');
      return;
    }

    if (!Capacitor.isNativePlatform()) {
      console.log('ℹ️ Not a native platform, skipping push notification initialization');
      this.isInitialized = true;
      return;
    }

    try {
      console.log('🔔 Starting push notification initialization...');

      // Check if push notifications are available
      if (typeof PushNotifications === 'undefined') {
        throw new Error('PushNotifications plugin not available');
      }

      // Request permission first
      console.log('📋 Requesting permissions...');
      const permissionResult = await this.requestPermissions();

      if (permissionResult.receive !== 'granted') {
        console.warn('⚠️ Push notification permissions not granted');
        // Continue initialization but mark as initialized
        this.isInitialized = true;
        return;
      }

      // Register for push notifications
      console.log('📱 Registering for push notifications...');
      await PushNotifications.register();

      // Setup listeners
      console.log('👂 Setting up listeners...');
      this.setupListeners();

      this.isInitialized = true;
      console.log('✅ Push notifications initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize push notifications:', error);
      // Don't throw error, just log it and mark as initialized
      this.isInitialized = true;
    }
  }

  async requestPermissions(): Promise<PermissionStatus> {
    const permission = await PushNotifications.requestPermissions();
    
    if (permission.receive === 'granted') {
      console.log('✅ Push notification permissions granted');
    } else {
      console.log('❌ Push notification permissions denied');
    }

    return permission;
  }

  async checkPermissions(): Promise<PermissionStatus> {
    return await PushNotifications.checkPermissions();
  }

  private setupListeners(): void {
    // Token registration
    PushNotifications.addListener('registration', async (token: Token) => {
      console.log('📱 Push registration success, token:', token.value);
      this.currentToken = token.value;
      await this.saveTokenToServer(token.value);
    });

    // Registration error
    PushNotifications.addListener('registrationError', (error: any) => {
      console.error('❌ Push registration error:', error);
    });

    // Notification received (app in foreground)
    PushNotifications.addListener('pushNotificationReceived', 
      (notification: PushNotificationSchema) => {
        console.log('📨 Push notification received:', notification);
        this.handleForegroundNotification(notification);
      }
    );

    // Notification action performed (app opened from notification)
    PushNotifications.addListener('pushNotificationActionPerformed', 
      (notification: ActionPerformed) => {
        console.log('👆 Push notification action performed:', notification);
        this.handleNotificationAction(notification);
      }
    );
  }

  private async handleForegroundNotification(notification: PushNotificationSchema): Promise<void> {
    // Add to history
    const historyItem: NotificationHistory = {
      id: Date.now().toString(),
      title: notification.title || '',
      body: notification.body || '',
      data: notification.data as NotificationData,
      receivedAt: new Date().toISOString(),
      isRead: false
    };

    this.notificationHistory.unshift(historyItem);
    await this.saveNotificationHistory();

    // Show in-app notification if needed
    this.showInAppNotification(notification);

    // Update badge count
    await this.updateBadgeCount();
  }

  private async handleNotificationAction(action: ActionPerformed): Promise<void> {
    const { notification, actionId } = action;
    const data = notification.data as NotificationData;

    // Mark as read
    const historyItem = this.notificationHistory.find(
      item => item.data === data
    );
    if (historyItem) {
      historyItem.isRead = true;
      historyItem.actionTaken = actionId;
      historyItem.readAt = new Date().toISOString();
      await this.saveNotificationHistory();
    }

    // Handle different notification types
    switch (data?.type) {
      case 'chat_message':
        this.handleChatNotificationAction(data, actionId);
        break;
      case 'announcement':
        this.handleAnnouncementAction(data, actionId);
        break;
      case 'event_reminder':
        this.handleEventReminderAction(data, actionId);
        break;
      case 'dues_reminder':
        this.handleDuesReminderAction(data, actionId);
        break;
    }

    await this.updateBadgeCount();
  }

  private handleChatNotificationAction(data: any, actionId: string): void {
    // Navigate to chat
    window.dispatchEvent(new CustomEvent('navigate-to-chat', {
      detail: { chatId: data.chatId, messageId: data.messageId }
    }));
  }

  private handleAnnouncementAction(data: any, actionId: string): void {
    // Navigate to announcements or specific URL
    if (data.actionUrl) {
      window.dispatchEvent(new CustomEvent('navigate-to-url', {
        detail: { url: data.actionUrl }
      }));
    } else {
      window.dispatchEvent(new CustomEvent('navigate-to-announcements'));
    }
  }

  private handleEventReminderAction(data: any, actionId: string): void {
    // Navigate to events
    window.dispatchEvent(new CustomEvent('navigate-to-events', {
      detail: { eventId: data.eventId }
    }));
  }

  private handleDuesReminderAction(data: any, actionId: string): void {
    // Navigate to dues/finance page
    window.dispatchEvent(new CustomEvent('navigate-to-finance'));
  }

  private showInAppNotification(notification: PushNotificationSchema): void {
    // Create custom in-app notification
    const event = new CustomEvent('show-in-app-notification', {
      detail: {
        title: notification.title,
        body: notification.body,
        data: notification.data
      }
    });
    window.dispatchEvent(event);
  }

  async getToken(): Promise<string | null> {
    return this.currentToken;
  }

  private async saveTokenToServer(token: string): Promise<void> {
    try {
      const tokenData: PushNotificationToken = {
        token,
        platform: 'android',
        deviceId: await this.getDeviceId(),
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString()
      };

      // Save to Preferences for offline access
      await Preferences.set({
        key: 'push_token',
        value: JSON.stringify(tokenData)
      });

      // TODO: Send to your backend server
      console.log('💾 Token saved:', tokenData);
    } catch (error) {
      console.error('❌ Failed to save token:', error);
    }
  }

  private async getDeviceId(): Promise<string> {
    const { value } = await Preferences.get({ key: 'device_id' });
    if (value) {
      return value;
    }

    const deviceId = `android_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    await Preferences.set({ key: 'device_id', value: deviceId });
    return deviceId;
  }

  async getNotificationSettings(): Promise<NotificationSettings> {
    const { value } = await Preferences.get({ key: 'notification_settings' });
    
    if (value) {
      return JSON.parse(value);
    }

    // Default settings
    const defaultSettings: NotificationSettings = {
      chatMessages: true,
      announcements: true,
      eventReminders: true,
      duesReminders: true,
      sound: true,
      vibration: true,
      badge: true,
      customSounds: {
        chatMessage: 'default',
        announcement: 'announcement',
        eventReminder: 'reminder',
        duesReminder: 'urgent'
      }
    };

    await this.saveNotificationSettings(defaultSettings);
    return defaultSettings;
  }

  async saveNotificationSettings(settings: NotificationSettings): Promise<void> {
    await Preferences.set({
      key: 'notification_settings',
      value: JSON.stringify(settings)
    });
  }

  async getNotificationHistory(): Promise<NotificationHistory[]> {
    if (this.notificationHistory.length === 0) {
      const { value } = await Preferences.get({ key: 'notification_history' });
      if (value) {
        this.notificationHistory = JSON.parse(value);
      }
    }
    return this.notificationHistory;
  }

  private async saveNotificationHistory(): Promise<void> {
    // Keep only last 100 notifications
    if (this.notificationHistory.length > 100) {
      this.notificationHistory = this.notificationHistory.slice(0, 100);
    }

    await Preferences.set({
      key: 'notification_history',
      value: JSON.stringify(this.notificationHistory)
    });
  }

  async getUnreadCount(): Promise<number> {
    const history = await this.getNotificationHistory();
    return history.filter(item => !item.isRead).length;
  }

  async markAllAsRead(): Promise<void> {
    this.notificationHistory.forEach(item => {
      if (!item.isRead) {
        item.isRead = true;
        item.readAt = new Date().toISOString();
      }
    });
    await this.saveNotificationHistory();
    await this.updateBadgeCount();
  }

  private async updateBadgeCount(): Promise<void> {
    const unreadCount = await this.getUnreadCount();
    
    // Update app badge (Android)
    if (Capacitor.isNativePlatform()) {
      // Note: Badge plugin would be needed for this
      console.log(`📱 Badge count: ${unreadCount}`);
    }
  }

  async clearNotificationHistory(): Promise<void> {
    this.notificationHistory = [];
    await Preferences.remove({ key: 'notification_history' });
  }

  async unregister(): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      await PushNotifications.removeAllListeners();
      // Note: There's no unregister method in Capacitor Push Notifications
      // Token will remain valid until app is uninstalled
    }
    this.isInitialized = false;
    this.currentToken = null;
  }
}

export const pushNotificationService = new PushNotificationService();
