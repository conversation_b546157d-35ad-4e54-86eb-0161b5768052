import { getDatabase, ref, push, onValue, off, serverTimestamp, set } from 'firebase/database';
import app from '../lib/firebase';

export interface RealtimeChatMessage {
  id?: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: number;
  type: 'text' | 'image';
}

export interface UserPresence {
  online: boolean;
  lastSeen: number;
  name: string;
}

class FirebaseRealtimeChatService {
  private database = getDatabase(app);
  private readonly CHAT_ROOM = 'danapemuda-general-chat';
  private readonly PRESENCE_PATH = 'presence';
  
  // Send message to shared chat room
  async sendMessage(message: Omit<RealtimeChatMessage, 'id' | 'timestamp'>): Promise<void> {
    try {
      const messagesRef = ref(this.database, `chats/${this.CHAT_ROOM}/messages`);
      await push(messagesRef, {
        ...message,
        timestamp: serverTimestamp()
      });
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Listen for new messages
  onMessagesChange(callback: (messages: RealtimeChatMessage[]) => void): () => void {
    const messagesRef = ref(this.database, `chats/${this.CHAT_ROOM}/messages`);
    
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messagesList = Object.entries(data).map(([key, value]: [string, any]) => ({
          id: key,
          ...value,
          timestamp: value.timestamp || Date.now()
        })) as RealtimeChatMessage[];
        
        // Sort by timestamp
        messagesList.sort((a, b) => a.timestamp - b.timestamp);
        callback(messagesList);
      } else {
        callback([]);
      }
    });

    return () => off(messagesRef, 'value', unsubscribe);
  }

  // Set user presence (online/offline)
  async setUserPresence(userId: string, userName: string, online: boolean): Promise<void> {
    try {
      const presenceRef = ref(this.database, `${this.PRESENCE_PATH}/${userId}`);
      await set(presenceRef, {
        online,
        lastSeen: serverTimestamp(),
        name: userName
      });
    } catch (error) {
      console.error('Error setting user presence:', error);
    }
  }

  // Listen for user presence changes
  onPresenceChange(callback: (users: Record<string, UserPresence>) => void): () => void {
    const presenceRef = ref(this.database, this.PRESENCE_PATH);
    
    const unsubscribe = onValue(presenceRef, (snapshot) => {
      const data = snapshot.val() || {};
      callback(data);
    });

    return () => off(presenceRef, 'value', unsubscribe);
  }

  // Clean up user presence on disconnect
  async setupDisconnectHandler(userId: string): Promise<void> {
    try {
      const presenceRef = ref(this.database, `${this.PRESENCE_PATH}/${userId}`);
      // Note: onDisconnect is not available in v9, we'll handle this in the component
      // For now, we'll rely on component cleanup
    } catch (error) {
      console.error('Error setting up disconnect handler:', error);
    }
  }
}

export const firebaseRealtimeChatService = new FirebaseRealtimeChatService();
