/* Global Responsive System for All Components */

:root {
  /* Breakpoints */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  
  /* Spacing Scale */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 24px;
  --space-2xl: 32px;
  
  /* Safe Areas */
  --safe-top: env(safe-area-inset-top, 0px);
  --safe-bottom: env(safe-area-inset-bottom, 0px);
  --safe-left: env(safe-area-inset-left, 0px);
  --safe-right: env(safe-area-inset-right, 0px);
  
  /* Touch Targets */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;
}

/* Global Container Classes */
.responsive-container {
  width: 100vw;
  max-width: 100vw;
  min-width: 100vw;
  overflow-x: hidden;
  position: relative;
}

.responsive-page {
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  min-height: 100vh;
  min-height: 100dvh;
  max-height: 100vh;
  max-height: 100dvh;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  overflow-x: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.responsive-header {
  padding: max(var(--safe-top), var(--space-md)) var(--space-md) var(--space-md) var(--space-md);
  min-height: var(--touch-target-large);
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.responsive-content {
  flex: 1;
  min-height: 0;
  padding: var(--space-md);
}

.responsive-footer {
  padding: var(--space-md) var(--space-md) max(var(--safe-bottom), var(--space-md)) var(--space-md);
  flex-shrink: 0;
}

/* Touch-Friendly Elements */
.touch-button {
  min-width: var(--touch-target-min);
  min-height: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.touch-input {
  min-height: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
  font-size: 16px; /* Prevents zoom on iOS */
  border-radius: 8px;
  width: 100%;
}

/* Grid Systems */
.responsive-grid {
  display: grid;
  gap: var(--space-md);
  width: 100%;
}

.responsive-grid-1 { grid-template-columns: 1fr; }
.responsive-grid-2 { grid-template-columns: repeat(2, 1fr); }
.responsive-grid-3 { grid-template-columns: repeat(3, 1fr); }
.responsive-grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Text */
.text-responsive-xs { font-size: 12px; line-height: 1.4; }
.text-responsive-sm { font-size: 14px; line-height: 1.4; }
.text-responsive-base { font-size: 16px; line-height: 1.5; }
.text-responsive-lg { font-size: 18px; line-height: 1.5; }
.text-responsive-xl { font-size: 20px; line-height: 1.6; }
.text-responsive-2xl { font-size: 24px; line-height: 1.6; }

/* Responsive Spacing */
.space-responsive-xs { gap: var(--space-xs); }
.space-responsive-sm { gap: var(--space-sm); }
.space-responsive-md { gap: var(--space-md); }
.space-responsive-lg { gap: var(--space-lg); }
.space-responsive-xl { gap: var(--space-xl); }

/* Mobile Specific */
@media (max-width: 640px) {
  :root {
    --space-md: 8px;
    --space-lg: 12px;
    --space-xl: 16px;
  }
  
  .responsive-page {
    padding: max(var(--safe-top), var(--space-sm)) var(--space-sm) max(var(--safe-bottom), var(--space-sm)) var(--space-sm);
  }
  
  .responsive-header {
    padding: max(var(--safe-top), var(--space-sm)) var(--space-sm) var(--space-sm) var(--space-sm);
    min-height: var(--touch-target-comfortable);
  }
  
  .responsive-content {
    padding: var(--space-sm);
  }
  
  .responsive-grid-2 { grid-template-columns: 1fr; }
  .responsive-grid-3 { grid-template-columns: 1fr; }
  .responsive-grid-4 { grid-template-columns: repeat(2, 1fr); }
  
  .text-responsive-lg { font-size: 16px; }
  .text-responsive-xl { font-size: 18px; }
  .text-responsive-2xl { font-size: 20px; }
}

/* Very Small Screens */
@media (max-width: 360px) {
  :root {
    --space-md: 6px;
    --space-lg: 8px;
    --space-xl: 12px;
  }
  
  .responsive-grid-4 { grid-template-columns: 1fr; }
  .touch-button { padding: var(--space-xs) var(--space-sm); }
}

/* Tablet */
@media (min-width: 768px) {
  :root {
    --space-md: 16px;
    --space-lg: 20px;
    --space-xl: 28px;
  }
  
  .responsive-header {
    min-height: var(--touch-target-large);
  }
  
  .touch-button {
    min-width: var(--touch-target-comfortable);
    min-height: var(--touch-target-comfortable);
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .responsive-page {
    padding: var(--space-lg);
  }
  
  .responsive-content {
    padding: var(--space-lg);
  }
}

/* Landscape Orientation */
@media (orientation: landscape) and (max-height: 500px) {
  .responsive-header {
    min-height: var(--touch-target-min);
    padding: max(var(--safe-top), var(--space-xs)) var(--space-sm) var(--space-xs) var(--space-sm);
  }
  
  .responsive-content {
    padding: var(--space-xs) var(--space-sm);
  }
}

/* High DPI */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .touch-button {
    border-width: 1.5px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #404040;
  }
}

/* Focus Styles for Accessibility */
.touch-button:focus-visible,
.touch-input:focus-visible {
  outline: 2px solid #FCE09B;
  outline-offset: 2px;
}

/* Loading States */
.responsive-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: var(--space-xl);
}

/* Error States */
.responsive-error {
  padding: var(--space-lg);
  text-align: center;
  color: #FF9898;
}

/* Card Components */
.responsive-card {
  background: white;
  border: 4px solid #5D534B;
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 4px 4px 0px #5D534B;
  transition: all 0.2s ease;
}

.responsive-card:hover {
  box-shadow: 6px 6px 0px #5D534B;
  transform: translate(-2px, -2px);
}

@media (max-width: 640px) {
  .responsive-card {
    padding: var(--space-md);
    border-width: 3px;
    box-shadow: 3px 3px 0px #5D534B;
  }
  
  .responsive-card:hover {
    box-shadow: 4px 4px 0px #5D534B;
  }

  /* Mobile scroll optimizations */
  main {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Mobile scroll container - non-intrusive */
  .mobile-scroll-container {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    /* Don't force height, let content flow naturally */
  }

  /* Hide scrollbars on mobile */
  .mobile-scroll-container::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  .mobile-scroll-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Mobile scroll optimization for modal and content */
  .mobile-scroll-optimized {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  }

  .mobile-scroll-optimized::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  /* Mobile specific scrollbar for description */
  @media (max-width: 768px) {
    .scrollbar-thin {
      scrollbar-width: thin;
    }

    .scrollbar-thin::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    /* Support for Tailwind arbitrary value classes */
    .scrollbar-thumb-mint::-webkit-scrollbar-thumb {
      background-color: #9DE0D2;
      border-radius: 3px;
    }

    .scrollbar-track-light::-webkit-scrollbar-track {
      background-color: #f3f4f6;
      border-radius: 3px;
    }

    /* Mobile touch scrolling optimization */
    .max-h-64.overflow-y-auto {
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
      overscroll-behavior: contain;
    }

    /* Auto screen detection for all mobile devices */
    /* Universal safe area for all mobile screens */
    .mobile-screen-safe {
      padding-top: max(env(safe-area-inset-top), 24px);
      padding-bottom: max(env(safe-area-inset-bottom), 24px);
      padding-left: max(env(safe-area-inset-left), 16px);
      padding-right: max(env(safe-area-inset-right), 16px);
    }

    /* Header safe area - prevent cut off at top */
    .mobile-header-safe {
      padding-top: max(env(safe-area-inset-top), 28px);
      min-height: max(env(safe-area-inset-top), 28px);
    }

    /* Content safe area - prevent cut off */
    .mobile-content-safe {
      margin-top: max(env(safe-area-inset-top), 0px);
      margin-bottom: max(env(safe-area-inset-bottom), 0px);
      padding-top: 16px;
      padding-bottom: 16px;
    }

    /* Full screen safe area */
    .mobile-fullscreen-safe {
      height: 100vh;
      height: 100dvh;
      padding-top: max(env(safe-area-inset-top), 24px);
      padding-bottom: max(env(safe-area-inset-bottom), 24px);
    }
  }
}
