/* Mobile Chat Responsive Styles */

.mobile-chat-container {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
  max-height: 100vh;
  max-height: 100dvh;
  min-height: 100vh; /* Fallback for older browsers */
}

.mobile-chat-header {
  padding: max(env(safe-area-inset-top), 12px) 12px 12px 12px;
  min-height: 56px;
}

.mobile-chat-messages {
  padding-bottom: max(env(safe-area-inset-bottom), 8px);
}

.mobile-chat-input-area {
  padding: 12px 12px max(env(safe-area-inset-bottom), 12px) 12px;
}

.mobile-chat-textarea {
  min-height: 44px;
  max-height: 100px;
}

.mobile-chat-button {
  min-width: 44px;
  min-height: 44px;
}

/* Responsive breakpoints */
@media (max-width: 640px) {
  .mobile-chat-header {
    padding: max(env(safe-area-inset-top), 8px) 8px 8px 8px;
    min-height: 52px;
  }
  
  .mobile-chat-messages {
    padding: 8px;
    padding-bottom: max(env(safe-area-inset-bottom), 8px);
  }
  
  .mobile-chat-input-area {
    padding: 8px 8px max(env(safe-area-inset-bottom), 8px) 8px;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .mobile-chat-header {
    padding: max(env(safe-area-inset-top), 6px) 6px 6px 6px;
    min-height: 48px;
  }
  
  .mobile-chat-messages {
    padding: 6px;
    padding-bottom: max(env(safe-area-inset-bottom), 6px);
  }
  
  .mobile-chat-input-area {
    padding: 6px 6px max(env(safe-area-inset-bottom), 6px) 6px;
  }
}

/* Large screens */
@media (min-width: 768px) {
  .mobile-chat-header {
    padding: max(env(safe-area-inset-top), 16px) 16px 16px 16px;
    min-height: 64px;
  }
  
  .mobile-chat-messages {
    padding: 16px;
    padding-bottom: max(env(safe-area-inset-bottom), 16px);
  }
  
  .mobile-chat-input-area {
    padding: 16px 16px max(env(safe-area-inset-bottom), 16px) 16px;
  }
  
  .mobile-chat-textarea {
    min-height: 48px;
    max-height: 120px;
  }
  
  .mobile-chat-button {
    min-width: 48px;
    min-height: 48px;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-chat-header {
    min-height: 44px;
    padding: max(env(safe-area-inset-top), 4px) 8px 4px 8px;
  }
  
  .mobile-chat-messages {
    padding: 4px 8px;
    padding-bottom: max(env(safe-area-inset-bottom), 4px);
  }
  
  .mobile-chat-input-area {
    padding: 4px 8px max(env(safe-area-inset-bottom), 4px) 8px;
  }
  
  .mobile-chat-textarea {
    min-height: 36px;
    max-height: 72px;
  }
}

/* Safe area support for notched devices */
@supports (padding: max(0px)) {
  .mobile-chat-container {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Prevent zoom on input focus (iOS) */
@media screen and (max-width: 767px) {
  .mobile-chat-textarea,
  .mobile-chat-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-chat-button {
    border-width: 1.5px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobile-chat-container {
    background-color: #1a1a1a;
  }
  
  .mobile-chat-header {
    background-color: #2a2a2a;
    border-color: #404040;
  }
  
  .mobile-chat-messages {
    background-color: #1a1a1a;
  }
  
  .mobile-chat-input-area {
    background-color: #2a2a2a;
    border-color: #404040;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .mobile-chat-container * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
