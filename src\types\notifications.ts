export interface PushNotificationPayload {
  title: string;
  body: string;
  data?: Record<string, any>;
  badge?: number;
  sound?: string;
  icon?: string;
  image?: string;
  tag?: string;
  click_action?: string;
}

export interface ChatNotificationData {
  type: 'chat_message';
  chatId: string;
  senderId: string;
  senderName: string;
  messageId: string;
  messageText: string;
  timestamp: string;
}

export interface AnnouncementNotificationData {
  type: 'announcement';
  announcementId: string;
  title: string;
  content: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  timestamp: string;
  actionUrl?: string;
}

export interface EventNotificationData {
  type: 'event_reminder';
  eventId: string;
  eventTitle: string;
  eventDate: string;
  reminderType: 'day_before' | 'hour_before' | 'starting_now';
}

export interface DuesNotificationData {
  type: 'dues_reminder';
  memberId: string;
  memberName: string;
  amount: number;
  dueDate: string;
  monthsOverdue: number;
}

export type NotificationData = 
  | ChatNotificationData 
  | AnnouncementNotificationData 
  | EventNotificationData 
  | DuesNotificationData;

export interface NotificationPermissionStatus {
  granted: boolean;
  denied: boolean;
  prompt: boolean;
}

export interface PushNotificationToken {
  token: string;
  platform: 'android' | 'ios' | 'web';
  userId?: string;
  deviceId?: string;
  createdAt: string;
  lastUpdated: string;
}

export interface NotificationSettings {
  chatMessages: boolean;
  announcements: boolean;
  eventReminders: boolean;
  duesReminders: boolean;
  sound: boolean;
  vibration: boolean;
  badge: boolean;
  customSounds: {
    chatMessage: string;
    announcement: string;
    eventReminder: string;
    duesReminder: string;
  };
}

export interface NotificationAction {
  id: string;
  title: string;
  icon?: string;
  input?: boolean;
  inputPlaceholder?: string;
}

export interface LocalNotification {
  id: number;
  title: string;
  body: string;
  schedule?: {
    at: Date;
    repeats?: boolean;
    every?: 'minute' | 'hour' | 'day' | 'week' | 'month' | 'year';
  };
  sound?: string;
  attachments?: Array<{
    id: string;
    url: string;
    options?: Record<string, any>;
  }>;
  actionTypeId?: string;
  extra?: Record<string, any>;
}

export interface NotificationHistory {
  id: string;
  title: string;
  body: string;
  data: NotificationData;
  receivedAt: string;
  readAt?: string;
  actionTaken?: string;
  isRead: boolean;
}
